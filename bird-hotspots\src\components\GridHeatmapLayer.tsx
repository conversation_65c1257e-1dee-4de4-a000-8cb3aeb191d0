import { useEffect, useRef, useCallback } from 'react';
import L from 'leaflet';
import { useAppStore } from '../store/useAppStore';
import {
  getGridSize,
  getDisplayThreshold,
  aggregateObservationsToGrid,
  filterGridByBounds,
  limitGridCells,
  getGridStatsText
} from '../utils/gridUtils';
import {
  getColorByCount,
  getSquareSize,
  getSquareOpacity,
  getSquareClassName
} from '../utils/colorUtils';
import type { GridCell, HeatmapDisplayMode } from '../types';

interface GridHeatmapLayerProps {
  map: L.Map | null;
}

const GridHeatmapLayer = ({ map }: GridHeatmapLayerProps) => {
  const layerGroupRef = useRef<L.LayerGroup | null>(null);
  const currentZoomRef = useRef<number>(5);
  const currentModeRef = useRef<HeatmapDisplayMode>('grid');
  
  const { species, observations, searchQuery, mapZoom } = useAppStore();

  // 清理现有图层
  const clearLayers = useCallback(() => {
    if (layerGroupRef.current && map) {
      if (map.hasLayer(layerGroupRef.current)) {
        map.removeLayer(layerGroupRef.current);
      }
      layerGroupRef.current.clearLayers();
    }
  }, [map]);

  // 创建网格矩形
  const createGridRectangle = useCallback((gridCell: GridCell, speciesName: string): L.Rectangle => {
    const bounds: L.LatLngBoundsExpression = [
      [gridCell.bounds.south, gridCell.bounds.west],
      [gridCell.bounds.north, gridCell.bounds.east]
    ];

    const color = getColorByCount(gridCell.totalCount);
    const opacity = getSquareOpacity(gridCell.totalCount);

    const rectangle = L.rectangle(bounds, {
      fillColor: color,
      fillOpacity: opacity,
      color: '#ffffff',
      weight: 1,
      opacity: 0.8
    });

    // 添加弹窗
    rectangle.bindPopup(getGridStatsText(gridCell), {
      maxWidth: 250,
      className: 'grid-popup'
    });

    // 添加悬停效果
    rectangle.on('mouseover', function() {
      this.setStyle({
        weight: 2,
        opacity: 1,
        fillOpacity: Math.min(opacity + 0.2, 1)
      });
    });

    rectangle.on('mouseout', function() {
      this.setStyle({
        weight: 1,
        opacity: 0.8,
        fillOpacity: opacity
      });
    });

    return rectangle;
  }, []);

  // 创建点标记（高缩放级别时使用）
  const createPointMarker = useCallback((obs: any, speciesItem: any): L.Marker => {
    const squareSize = getSquareSize(obs.count);
    const squareColor = getColorByCount(obs.count);
    const squareOpacity = getSquareOpacity(obs.count);
    const squareClassName = getSquareClassName(obs.count);

    const squareIcon = L.divIcon({
      className: squareClassName,
      html: `<div style="
        width: ${squareSize}px;
        height: ${squareSize}px;
        background-color: ${squareColor};
        opacity: ${squareOpacity};
        border-radius: 2px;
        border: 1px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      "></div>`,
      iconSize: [squareSize, squareSize],
      iconAnchor: [squareSize / 2, squareSize / 2],
      popupAnchor: [0, -squareSize / 2]
    });

    const marker = L.marker([obs.lat, obs.lng], {
      icon: squareIcon,
      riseOnHover: true
    });

    marker.bindPopup(`
      <div class="text-sm p-2">
        <div class="font-bold text-gray-800 mb-2">${speciesItem.name}</div>
        <div class="text-gray-600 italic mb-2">${speciesItem.scientificName}</div>
        <div class="flex items-center justify-between">
          <span class="text-gray-700">观察数量:</span>
          <span class="font-bold text-blue-600">${obs.count}</span>
        </div>
        <div class="flex items-center justify-between mt-1">
          <span class="text-gray-700">强度:</span>
          <span class="font-medium text-green-600">${(obs.intensity * 100).toFixed(1)}%</span>
        </div>
      </div>
    `, {
      maxWidth: 200,
      className: 'hotspot-popup'
    });

    return marker;
  }, []);

  // 渲染网格热力图
  const renderGridHeatmap = useCallback(() => {
    if (!map) return;

    clearLayers();

    // 创建新的图层组
    const layerGroup = L.layerGroup();
    layerGroupRef.current = layerGroup;

    const currentZoom = map.getZoom();
    const displayMode = getDisplayThreshold(currentZoom);
    const gridSize = getGridSize(currentZoom);

    // 过滤可见物种
    const filteredSpecies = species.filter(speciesItem => {
      if (!speciesItem.isVisible) return false;

      if (searchQuery.trim()) {
        const query = searchQuery.toLowerCase();
        return speciesItem.name.toLowerCase().includes(query) ||
               speciesItem.scientificName.toLowerCase().includes(query);
      }

      return true;
    });

    if (displayMode === 'points') {
      // 高缩放级别：显示具体观察点
      filteredSpecies.forEach(speciesItem => {
        const speciesObservations = observations.filter(
          obs => obs.speciesId === speciesItem.id
        );

        speciesObservations.forEach(obs => {
          const marker = createPointMarker(obs, speciesItem);
          layerGroup.addLayer(marker);
        });
      });
    } else {
      // 低缩放级别：显示网格聚合
      const visibleSpeciesIds = filteredSpecies.map(s => s.id);
      const gridMap = aggregateObservationsToGrid(observations, gridSize, visibleSpeciesIds);

      // 获取地图边界
      const bounds = map.getBounds();
      const mapBounds = {
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      };

      // 过滤可见网格
      const visibleGrid = filterGridByBounds(gridMap, mapBounds);
      
      // 限制网格数量以提高性能
      const mapCenter = map.getCenter();
      const limitedGrid = limitGridCells(visibleGrid, 1000, [mapCenter.lat, mapCenter.lng]);

      // 创建网格矩形
      limitedGrid.forEach(gridCell => {
        if (gridCell.totalCount > 0) {
          const rectangle = createGridRectangle(gridCell, '多物种聚合');
          layerGroup.addLayer(rectangle);
        }
      });
    }

    // 添加到地图
    layerGroup.addTo(map);
    
    // 更新当前状态
    currentZoomRef.current = currentZoom;
    currentModeRef.current = displayMode;
  }, [map, species, observations, searchQuery, clearLayers, createGridRectangle, createPointMarker]);

  // 处理缩放变化
  const handleZoomChange = useCallback(() => {
    if (!map) return;
    
    const newZoom = map.getZoom();
    const newMode = getDisplayThreshold(newZoom);
    
    // 只有当缩放级别变化足够大或显示模式改变时才重新渲染
    if (Math.abs(newZoom - currentZoomRef.current) >= 1 || newMode !== currentModeRef.current) {
      renderGridHeatmap();
    }
  }, [map, renderGridHeatmap]);

  // 处理地图移动
  const handleMapMove = useCallback(() => {
    if (!map) return;
    
    // 在网格模式下，地图移动时需要重新渲染以显示新区域的网格
    if (currentModeRef.current === 'grid') {
      renderGridHeatmap();
    }
  }, [map, renderGridHeatmap]);

  // 主要效果：监听数据变化和地图事件
  useEffect(() => {
    if (!map) return;

    // 初始渲染
    renderGridHeatmap();

    // 添加事件监听器
    map.on('zoomend', handleZoomChange);
    map.on('moveend', handleMapMove);

    // 清理函数
    return () => {
      map.off('zoomend', handleZoomChange);
      map.off('moveend', handleMapMove);
      clearLayers();
    };
  }, [map, species, observations, searchQuery, renderGridHeatmap, handleZoomChange, handleMapMove, clearLayers]);

  // 监听缩放状态变化（来自store）
  useEffect(() => {
    if (map && mapZoom !== currentZoomRef.current) {
      handleZoomChange();
    }
  }, [map, mapZoom, handleZoomChange]);

  return null; // 这个组件不渲染任何DOM元素
};

export default GridHeatmapLayer;
