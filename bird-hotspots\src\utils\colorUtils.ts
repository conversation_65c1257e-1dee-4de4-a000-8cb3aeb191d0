/**
 * 颜色映射工具函数
 * 用于根据观察数量生成颜色梯度
 */

export interface ColorRange {
  min: number;
  max: number;
  color: string;
  label: string;
}

/**
 * 预定义的颜色范围配置
 * 参考 eBird 网站的设计风格
 */
export const COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#e8f5e8', label: '0-15' },
  { min: 16, max: 50, color: '#c8e6c9', label: '16-50' },
  { min: 51, max: 100, color: '#a5d6a7', label: '51-100' },
  { min: 101, max: 150, color: '#81c784', label: '101-150' },
  { min: 151, max: 200, color: '#66bb6a', label: '151-200' },
  { min: 201, max: 300, color: '#4caf50', label: '201-300' },
  { min: 301, max: 400, color: '#43a047', label: '301-400' },
  { min: 401, max: 500, color: '#388e3c', label: '401-500' },
  { min: 501, max: 600, color: '#2e7d32', label: '501-600' },
  { min: 601, max: Infinity, color: '#1b5e20', label: '600+' }
];

/**
 * 替代颜色方案 - 蓝色系
 */
export const BLUE_COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#e3f2fd', label: '0-15' },
  { min: 16, max: 50, color: '#bbdefb', label: '16-50' },
  { min: 51, max: 100, color: '#90caf9', label: '51-100' },
  { min: 101, max: 150, color: '#64b5f6', label: '101-150' },
  { min: 151, max: 200, color: '#42a5f5', label: '151-200' },
  { min: 201, max: 300, color: '#2196f3', label: '201-300' },
  { min: 301, max: 400, color: '#1e88e5', label: '301-400' },
  { min: 401, max: 500, color: '#1976d2', label: '401-500' },
  { min: 501, max: 600, color: '#1565c0', label: '501-600' },
  { min: 601, max: Infinity, color: '#0d47a1', label: '600+' }
];

/**
 * 替代颜色方案 - 橙红色系
 */
export const ORANGE_COLOR_RANGES: ColorRange[] = [
  { min: 0, max: 15, color: '#fff3e0', label: '0-15' },
  { min: 16, max: 50, color: '#ffe0b2', label: '16-50' },
  { min: 51, max: 100, color: '#ffcc80', label: '51-100' },
  { min: 101, max: 150, color: '#ffb74d', label: '101-150' },
  { min: 151, max: 200, color: '#ffa726', label: '151-200' },
  { min: 201, max: 300, color: '#ff9800', label: '201-300' },
  { min: 301, max: 400, color: '#fb8c00', label: '301-400' },
  { min: 401, max: 500, color: '#f57c00', label: '401-500' },
  { min: 501, max: 600, color: '#ef6c00', label: '501-600' },
  { min: 601, max: Infinity, color: '#e65100', label: '600+' }
];

/**
 * 根据观察数量获取对应的颜色
 * @param count 观察数量
 * @param colorRanges 颜色范围配置，默认使用绿色系
 * @returns 对应的颜色值
 */
export function getColorByCount(count: number, colorRanges: ColorRange[] = COLOR_RANGES): string {
  const range = colorRanges.find(range => count >= range.min && count <= range.max);
  return range ? range.color : colorRanges[colorRanges.length - 1].color;
}

/**
 * 根据观察数量获取对应的颜色范围信息
 * @param count 观察数量
 * @param colorRanges 颜色范围配置
 * @returns 对应的颜色范围对象
 */
export function getColorRangeByCount(count: number, colorRanges: ColorRange[] = COLOR_RANGES): ColorRange {
  const range = colorRanges.find(range => count >= range.min && count <= range.max);
  return range || colorRanges[colorRanges.length - 1];
}

/**
 * 计算方块的大小
 * @param count 观察数量
 * @param minSize 最小尺寸（像素）
 * @param maxSize 最大尺寸（像素）
 * @returns 方块的尺寸
 */
export function getSquareSize(count: number, minSize: number = 8, maxSize: number = 24): number {
  // 使用对数刻度来计算大小，避免极值过于突出
  const logCount = Math.log10(Math.max(1, count));
  const maxLogCount = Math.log10(1000); // 假设最大值为1000
  const ratio = Math.min(logCount / maxLogCount, 1);
  
  return Math.round(minSize + (maxSize - minSize) * ratio);
}

/**
 * 获取方块的透明度
 * @param count 观察数量
 * @param minOpacity 最小透明度
 * @param maxOpacity 最大透明度
 * @returns 透明度值
 */
export function getSquareOpacity(count: number, minOpacity: number = 0.6, maxOpacity: number = 0.9): number {
  const logCount = Math.log10(Math.max(1, count));
  const maxLogCount = Math.log10(1000);
  const ratio = Math.min(logCount / maxLogCount, 1);
  
  return minOpacity + (maxOpacity - minOpacity) * ratio;
}

/**
 * 生成方块热点的 CSS 类名
 * @param count 观察数量
 * @returns CSS 类名
 */
export function getSquareClassName(count: number): string {
  const size = getSquareSize(count);
  return `hotspot-square hotspot-size-${size}`;
}
