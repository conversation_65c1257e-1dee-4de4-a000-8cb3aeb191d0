(function(){const E=document.createElement("link").relList;if(E&&E.supports&&E.supports("modulepreload"))return;for(const B of document.querySelectorAll('link[rel="modulepreload"]'))v(B);new MutationObserver(B=>{for(const V of B)if(V.type==="childList")for(const k of V.addedNodes)k.tagName==="LINK"&&k.rel==="modulepreload"&&v(k)}).observe(document,{childList:!0,subtree:!0});function b(B){const V={};return B.integrity&&(V.integrity=B.integrity),B.referrerPolicy&&(V.referrerPolicy=B.referrerPolicy),B.crossOrigin==="use-credentials"?V.credentials="include":B.crossOrigin==="anonymous"?V.credentials="omit":V.credentials="same-origin",V}function v(B){if(B.ep)return;B.ep=!0;const V=b(B);fetch(B.href,V)}})();function Ed(g){return g&&g.__esModule&&Object.prototype.hasOwnProperty.call(g,"default")?g.default:g}var mc={exports:{}},Hs={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cd;function Vm(){if(cd)return Hs;cd=1;var g=Symbol.for("react.transitional.element"),E=Symbol.for("react.fragment");function b(v,B,V){var k=null;if(V!==void 0&&(k=""+V),B.key!==void 0&&(k=""+B.key),"key"in B){V={};for(var ct in B)ct!=="key"&&(V[ct]=B[ct])}else V=B;return B=V.ref,{$$typeof:g,type:v,key:k,ref:B!==void 0?B:null,props:V}}return Hs.Fragment=E,Hs.jsx=b,Hs.jsxs=b,Hs}var fd;function Xm(){return fd||(fd=1,mc.exports=Vm()),mc.exports}var x=Xm(),_c={exports:{}},ft={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hd;function Qm(){if(hd)return ft;hd=1;var g=Symbol.for("react.transitional.element"),E=Symbol.for("react.portal"),b=Symbol.for("react.fragment"),v=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),V=Symbol.for("react.consumer"),k=Symbol.for("react.context"),ct=Symbol.for("react.forward_ref"),A=Symbol.for("react.suspense"),C=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),I=Symbol.iterator;function ut(m){return m===null||typeof m!="object"?null:(m=I&&m[I]||m["@@iterator"],typeof m=="function"?m:null)}var kt={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},jt=Object.assign,mt={};function Xt(m,R,Y){this.props=m,this.context=R,this.refs=mt,this.updater=Y||kt}Xt.prototype.isReactComponent={},Xt.prototype.setState=function(m,R){if(typeof m!="object"&&typeof m!="function"&&m!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,m,R,"setState")},Xt.prototype.forceUpdate=function(m){this.updater.enqueueForceUpdate(this,m,"forceUpdate")};function ne(){}ne.prototype=Xt.prototype;function Ae(m,R,Y){this.props=m,this.context=R,this.refs=mt,this.updater=Y||kt}var wt=Ae.prototype=new ne;wt.constructor=Ae,jt(wt,Xt.prototype),wt.isPureReactComponent=!0;var fe=Array.isArray,ht={H:null,A:null,T:null,S:null,V:null},he=Object.prototype.hasOwnProperty;function be(m,R,Y,G,J,$){return Y=$.ref,{$$typeof:g,type:m,key:R,ref:Y!==void 0?Y:null,props:$}}function xe(m,R){return be(m.type,R,void 0,void 0,void 0,m.props)}function je(m){return typeof m=="object"&&m!==null&&m.$$typeof===g}function Hi(m){var R={"=":"=0",":":"=2"};return"$"+m.replace(/[=:]/g,function(Y){return R[Y]})}var Ut=/\/+/g;function Et(m,R){return typeof m=="object"&&m!==null&&m.key!=null?Hi(""+m.key):R.toString(36)}function qi(){}function de(m){switch(m.status){case"fulfilled":return m.value;case"rejected":throw m.reason;default:switch(typeof m.status=="string"?m.then(qi,qi):(m.status="pending",m.then(function(R){m.status==="pending"&&(m.status="fulfilled",m.value=R)},function(R){m.status==="pending"&&(m.status="rejected",m.reason=R)})),m.status){case"fulfilled":return m.value;case"rejected":throw m.reason}}throw m}function ae(m,R,Y,G,J){var $=typeof m;($==="undefined"||$==="boolean")&&(m=null);var X=!1;if(m===null)X=!0;else switch($){case"bigint":case"string":case"number":X=!0;break;case"object":switch(m.$$typeof){case g:case E:X=!0;break;case q:return X=m._init,ae(X(m._payload),R,Y,G,J)}}if(X)return J=J(m),X=G===""?"."+Et(m,0):G,fe(J)?(Y="",X!=null&&(Y=X.replace(Ut,"$&/")+"/"),ae(J,R,Y,"",function(mi){return mi})):J!=null&&(je(J)&&(J=xe(J,Y+(J.key==null||m&&m.key===J.key?"":(""+J.key).replace(Ut,"$&/")+"/")+X)),R.push(J)),1;X=0;var Pt=G===""?".":G+":";if(fe(m))for(var xt=0;xt<m.length;xt++)G=m[xt],$=Pt+Et(G,xt),X+=ae(G,R,Y,$,J);else if(xt=ut(m),typeof xt=="function")for(m=xt.call(m),xt=0;!(G=m.next()).done;)G=G.value,$=Pt+Et(G,xt++),X+=ae(G,R,Y,$,J);else if($==="object"){if(typeof m.then=="function")return ae(de(m),R,Y,G,J);throw R=String(m),Error("Objects are not valid as a React child (found: "+(R==="[object Object]"?"object with keys {"+Object.keys(m).join(", ")+"}":R)+"). If you meant to render a collection of children, use an array instead.")}return X}function D(m,R,Y){if(m==null)return m;var G=[],J=0;return ae(m,G,"","",function($){return R.call(Y,$,J++)}),G}function Q(m){if(m._status===-1){var R=m._result;R=R(),R.then(function(Y){(m._status===0||m._status===-1)&&(m._status=1,m._result=Y)},function(Y){(m._status===0||m._status===-1)&&(m._status=2,m._result=Y)}),m._status===-1&&(m._status=0,m._result=R)}if(m._status===1)return m._result.default;throw m._result}var P=typeof reportError=="function"?reportError:function(m){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var R=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof m=="object"&&m!==null&&typeof m.message=="string"?String(m.message):String(m),error:m});if(!window.dispatchEvent(R))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",m);return}console.error(m)};function Mt(){}return ft.Children={map:D,forEach:function(m,R,Y){D(m,function(){R.apply(this,arguments)},Y)},count:function(m){var R=0;return D(m,function(){R++}),R},toArray:function(m){return D(m,function(R){return R})||[]},only:function(m){if(!je(m))throw Error("React.Children.only expected to receive a single React element child.");return m}},ft.Component=Xt,ft.Fragment=b,ft.Profiler=B,ft.PureComponent=Ae,ft.StrictMode=v,ft.Suspense=A,ft.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ht,ft.__COMPILER_RUNTIME={__proto__:null,c:function(m){return ht.H.useMemoCache(m)}},ft.cache=function(m){return function(){return m.apply(null,arguments)}},ft.cloneElement=function(m,R,Y){if(m==null)throw Error("The argument must be a React element, but you passed "+m+".");var G=jt({},m.props),J=m.key,$=void 0;if(R!=null)for(X in R.ref!==void 0&&($=void 0),R.key!==void 0&&(J=""+R.key),R)!he.call(R,X)||X==="key"||X==="__self"||X==="__source"||X==="ref"&&R.ref===void 0||(G[X]=R[X]);var X=arguments.length-2;if(X===1)G.children=Y;else if(1<X){for(var Pt=Array(X),xt=0;xt<X;xt++)Pt[xt]=arguments[xt+2];G.children=Pt}return be(m.type,J,void 0,void 0,$,G)},ft.createContext=function(m){return m={$$typeof:k,_currentValue:m,_currentValue2:m,_threadCount:0,Provider:null,Consumer:null},m.Provider=m,m.Consumer={$$typeof:V,_context:m},m},ft.createElement=function(m,R,Y){var G,J={},$=null;if(R!=null)for(G in R.key!==void 0&&($=""+R.key),R)he.call(R,G)&&G!=="key"&&G!=="__self"&&G!=="__source"&&(J[G]=R[G]);var X=arguments.length-2;if(X===1)J.children=Y;else if(1<X){for(var Pt=Array(X),xt=0;xt<X;xt++)Pt[xt]=arguments[xt+2];J.children=Pt}if(m&&m.defaultProps)for(G in X=m.defaultProps,X)J[G]===void 0&&(J[G]=X[G]);return be(m,$,void 0,void 0,null,J)},ft.createRef=function(){return{current:null}},ft.forwardRef=function(m){return{$$typeof:ct,render:m}},ft.isValidElement=je,ft.lazy=function(m){return{$$typeof:q,_payload:{_status:-1,_result:m},_init:Q}},ft.memo=function(m,R){return{$$typeof:C,type:m,compare:R===void 0?null:R}},ft.startTransition=function(m){var R=ht.T,Y={};ht.T=Y;try{var G=m(),J=ht.S;J!==null&&J(Y,G),typeof G=="object"&&G!==null&&typeof G.then=="function"&&G.then(Mt,P)}catch($){P($)}finally{ht.T=R}},ft.unstable_useCacheRefresh=function(){return ht.H.useCacheRefresh()},ft.use=function(m){return ht.H.use(m)},ft.useActionState=function(m,R,Y){return ht.H.useActionState(m,R,Y)},ft.useCallback=function(m,R){return ht.H.useCallback(m,R)},ft.useContext=function(m){return ht.H.useContext(m)},ft.useDebugValue=function(){},ft.useDeferredValue=function(m,R){return ht.H.useDeferredValue(m,R)},ft.useEffect=function(m,R,Y){var G=ht.H;if(typeof Y=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return G.useEffect(m,R)},ft.useId=function(){return ht.H.useId()},ft.useImperativeHandle=function(m,R,Y){return ht.H.useImperativeHandle(m,R,Y)},ft.useInsertionEffect=function(m,R){return ht.H.useInsertionEffect(m,R)},ft.useLayoutEffect=function(m,R){return ht.H.useLayoutEffect(m,R)},ft.useMemo=function(m,R){return ht.H.useMemo(m,R)},ft.useOptimistic=function(m,R){return ht.H.useOptimistic(m,R)},ft.useReducer=function(m,R,Y){return ht.H.useReducer(m,R,Y)},ft.useRef=function(m){return ht.H.useRef(m)},ft.useState=function(m){return ht.H.useState(m)},ft.useSyncExternalStore=function(m,R,Y){return ht.H.useSyncExternalStore(m,R,Y)},ft.useTransition=function(){return ht.H.useTransition()},ft.version="19.1.0",ft}var dd;function xc(){return dd||(dd=1,_c.exports=Qm()),_c.exports}var ie=xc();const md=Ed(ie);var pc={exports:{}},qs={},vc={exports:{}},gc={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _d;function Km(){return _d||(_d=1,function(g){function E(D,Q){var P=D.length;D.push(Q);t:for(;0<P;){var Mt=P-1>>>1,m=D[Mt];if(0<B(m,Q))D[Mt]=Q,D[P]=m,P=Mt;else break t}}function b(D){return D.length===0?null:D[0]}function v(D){if(D.length===0)return null;var Q=D[0],P=D.pop();if(P!==Q){D[0]=P;t:for(var Mt=0,m=D.length,R=m>>>1;Mt<R;){var Y=2*(Mt+1)-1,G=D[Y],J=Y+1,$=D[J];if(0>B(G,P))J<m&&0>B($,G)?(D[Mt]=$,D[J]=P,Mt=J):(D[Mt]=G,D[Y]=P,Mt=Y);else if(J<m&&0>B($,P))D[Mt]=$,D[J]=P,Mt=J;else break t}}return Q}function B(D,Q){var P=D.sortIndex-Q.sortIndex;return P!==0?P:D.id-Q.id}if(g.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var V=performance;g.unstable_now=function(){return V.now()}}else{var k=Date,ct=k.now();g.unstable_now=function(){return k.now()-ct}}var A=[],C=[],q=1,I=null,ut=3,kt=!1,jt=!1,mt=!1,Xt=!1,ne=typeof setTimeout=="function"?setTimeout:null,Ae=typeof clearTimeout=="function"?clearTimeout:null,wt=typeof setImmediate<"u"?setImmediate:null;function fe(D){for(var Q=b(C);Q!==null;){if(Q.callback===null)v(C);else if(Q.startTime<=D)v(C),Q.sortIndex=Q.expirationTime,E(A,Q);else break;Q=b(C)}}function ht(D){if(mt=!1,fe(D),!jt)if(b(A)!==null)jt=!0,he||(he=!0,Et());else{var Q=b(C);Q!==null&&ae(ht,Q.startTime-D)}}var he=!1,be=-1,xe=5,je=-1;function Hi(){return Xt?!0:!(g.unstable_now()-je<xe)}function Ut(){if(Xt=!1,he){var D=g.unstable_now();je=D;var Q=!0;try{t:{jt=!1,mt&&(mt=!1,Ae(be),be=-1),kt=!0;var P=ut;try{e:{for(fe(D),I=b(A);I!==null&&!(I.expirationTime>D&&Hi());){var Mt=I.callback;if(typeof Mt=="function"){I.callback=null,ut=I.priorityLevel;var m=Mt(I.expirationTime<=D);if(D=g.unstable_now(),typeof m=="function"){I.callback=m,fe(D),Q=!0;break e}I===b(A)&&v(A),fe(D)}else v(A);I=b(A)}if(I!==null)Q=!0;else{var R=b(C);R!==null&&ae(ht,R.startTime-D),Q=!1}}break t}finally{I=null,ut=P,kt=!1}Q=void 0}}finally{Q?Et():he=!1}}}var Et;if(typeof wt=="function")Et=function(){wt(Ut)};else if(typeof MessageChannel<"u"){var qi=new MessageChannel,de=qi.port2;qi.port1.onmessage=Ut,Et=function(){de.postMessage(null)}}else Et=function(){ne(Ut,0)};function ae(D,Q){be=ne(function(){D(g.unstable_now())},Q)}g.unstable_IdlePriority=5,g.unstable_ImmediatePriority=1,g.unstable_LowPriority=4,g.unstable_NormalPriority=3,g.unstable_Profiling=null,g.unstable_UserBlockingPriority=2,g.unstable_cancelCallback=function(D){D.callback=null},g.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):xe=0<D?Math.floor(1e3/D):5},g.unstable_getCurrentPriorityLevel=function(){return ut},g.unstable_next=function(D){switch(ut){case 1:case 2:case 3:var Q=3;break;default:Q=ut}var P=ut;ut=Q;try{return D()}finally{ut=P}},g.unstable_requestPaint=function(){Xt=!0},g.unstable_runWithPriority=function(D,Q){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var P=ut;ut=D;try{return Q()}finally{ut=P}},g.unstable_scheduleCallback=function(D,Q,P){var Mt=g.unstable_now();switch(typeof P=="object"&&P!==null?(P=P.delay,P=typeof P=="number"&&0<P?Mt+P:Mt):P=Mt,D){case 1:var m=-1;break;case 2:m=250;break;case 5:m=1073741823;break;case 4:m=1e4;break;default:m=5e3}return m=P+m,D={id:q++,callback:Q,priorityLevel:D,startTime:P,expirationTime:m,sortIndex:-1},P>Mt?(D.sortIndex=P,E(C,D),b(A)===null&&D===b(C)&&(mt?(Ae(be),be=-1):mt=!0,ae(ht,P-Mt))):(D.sortIndex=m,E(A,D),jt||kt||(jt=!0,he||(he=!0,Et()))),D},g.unstable_shouldYield=Hi,g.unstable_wrapCallback=function(D){var Q=ut;return function(){var P=ut;ut=Q;try{return D.apply(this,arguments)}finally{ut=P}}}}(gc)),gc}var pd;function Jm(){return pd||(pd=1,vc.exports=Km()),vc.exports}var yc={exports:{}},ze={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vd;function Wm(){if(vd)return ze;vd=1;var g=xc();function E(A){var C="https://react.dev/errors/"+A;if(1<arguments.length){C+="?args[]="+encodeURIComponent(arguments[1]);for(var q=2;q<arguments.length;q++)C+="&args[]="+encodeURIComponent(arguments[q])}return"Minified React error #"+A+"; visit "+C+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function b(){}var v={d:{f:b,r:function(){throw Error(E(522))},D:b,C:b,L:b,m:b,X:b,S:b,M:b},p:0,findDOMNode:null},B=Symbol.for("react.portal");function V(A,C,q){var I=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:B,key:I==null?null:""+I,children:A,containerInfo:C,implementation:q}}var k=g.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function ct(A,C){if(A==="font")return"";if(typeof C=="string")return C==="use-credentials"?C:""}return ze.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=v,ze.createPortal=function(A,C){var q=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!C||C.nodeType!==1&&C.nodeType!==9&&C.nodeType!==11)throw Error(E(299));return V(A,C,null,q)},ze.flushSync=function(A){var C=k.T,q=v.p;try{if(k.T=null,v.p=2,A)return A()}finally{k.T=C,v.p=q,v.d.f()}},ze.preconnect=function(A,C){typeof A=="string"&&(C?(C=C.crossOrigin,C=typeof C=="string"?C==="use-credentials"?C:"":void 0):C=null,v.d.C(A,C))},ze.prefetchDNS=function(A){typeof A=="string"&&v.d.D(A)},ze.preinit=function(A,C){if(typeof A=="string"&&C&&typeof C.as=="string"){var q=C.as,I=ct(q,C.crossOrigin),ut=typeof C.integrity=="string"?C.integrity:void 0,kt=typeof C.fetchPriority=="string"?C.fetchPriority:void 0;q==="style"?v.d.S(A,typeof C.precedence=="string"?C.precedence:void 0,{crossOrigin:I,integrity:ut,fetchPriority:kt}):q==="script"&&v.d.X(A,{crossOrigin:I,integrity:ut,fetchPriority:kt,nonce:typeof C.nonce=="string"?C.nonce:void 0})}},ze.preinitModule=function(A,C){if(typeof A=="string")if(typeof C=="object"&&C!==null){if(C.as==null||C.as==="script"){var q=ct(C.as,C.crossOrigin);v.d.M(A,{crossOrigin:q,integrity:typeof C.integrity=="string"?C.integrity:void 0,nonce:typeof C.nonce=="string"?C.nonce:void 0})}}else C==null&&v.d.M(A)},ze.preload=function(A,C){if(typeof A=="string"&&typeof C=="object"&&C!==null&&typeof C.as=="string"){var q=C.as,I=ct(q,C.crossOrigin);v.d.L(A,q,{crossOrigin:I,integrity:typeof C.integrity=="string"?C.integrity:void 0,nonce:typeof C.nonce=="string"?C.nonce:void 0,type:typeof C.type=="string"?C.type:void 0,fetchPriority:typeof C.fetchPriority=="string"?C.fetchPriority:void 0,referrerPolicy:typeof C.referrerPolicy=="string"?C.referrerPolicy:void 0,imageSrcSet:typeof C.imageSrcSet=="string"?C.imageSrcSet:void 0,imageSizes:typeof C.imageSizes=="string"?C.imageSizes:void 0,media:typeof C.media=="string"?C.media:void 0})}},ze.preloadModule=function(A,C){if(typeof A=="string")if(C){var q=ct(C.as,C.crossOrigin);v.d.m(A,{as:typeof C.as=="string"&&C.as!=="script"?C.as:void 0,crossOrigin:q,integrity:typeof C.integrity=="string"?C.integrity:void 0})}else v.d.m(A)},ze.requestFormReset=function(A){v.d.r(A)},ze.unstable_batchedUpdates=function(A,C){return A(C)},ze.useFormState=function(A,C,q){return k.H.useFormState(A,C,q)},ze.useFormStatus=function(){return k.H.useHostTransitionStatus()},ze.version="19.1.0",ze}var gd;function Fm(){if(gd)return yc.exports;gd=1;function g(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(g)}catch(E){console.error(E)}}return g(),yc.exports=Wm(),yc.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yd;function Im(){if(yd)return qs;yd=1;var g=Jm(),E=xc(),b=Fm();function v(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function B(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function V(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function k(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function ct(t){if(V(t)!==t)throw Error(v(188))}function A(t){var e=t.alternate;if(!e){if(e=V(t),e===null)throw Error(v(188));return e!==t?null:t}for(var n=t,l=e;;){var o=n.return;if(o===null)break;var u=o.alternate;if(u===null){if(l=o.return,l!==null){n=l;continue}break}if(o.child===u.child){for(u=o.child;u;){if(u===n)return ct(o),t;if(u===l)return ct(o),e;u=u.sibling}throw Error(v(188))}if(n.return!==l.return)n=o,l=u;else{for(var f=!1,d=o.child;d;){if(d===n){f=!0,n=o,l=u;break}if(d===l){f=!0,l=o,n=u;break}d=d.sibling}if(!f){for(d=u.child;d;){if(d===n){f=!0,n=u,l=o;break}if(d===l){f=!0,l=u,n=o;break}d=d.sibling}if(!f)throw Error(v(189))}}if(n.alternate!==l)throw Error(v(190))}if(n.tag!==3)throw Error(v(188));return n.stateNode.current===n?t:e}function C(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=C(t),e!==null)return e;t=t.sibling}return null}var q=Object.assign,I=Symbol.for("react.element"),ut=Symbol.for("react.transitional.element"),kt=Symbol.for("react.portal"),jt=Symbol.for("react.fragment"),mt=Symbol.for("react.strict_mode"),Xt=Symbol.for("react.profiler"),ne=Symbol.for("react.provider"),Ae=Symbol.for("react.consumer"),wt=Symbol.for("react.context"),fe=Symbol.for("react.forward_ref"),ht=Symbol.for("react.suspense"),he=Symbol.for("react.suspense_list"),be=Symbol.for("react.memo"),xe=Symbol.for("react.lazy"),je=Symbol.for("react.activity"),Hi=Symbol.for("react.memo_cache_sentinel"),Ut=Symbol.iterator;function Et(t){return t===null||typeof t!="object"?null:(t=Ut&&t[Ut]||t["@@iterator"],typeof t=="function"?t:null)}var qi=Symbol.for("react.client.reference");function de(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===qi?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case jt:return"Fragment";case Xt:return"Profiler";case mt:return"StrictMode";case ht:return"Suspense";case he:return"SuspenseList";case je:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case kt:return"Portal";case wt:return(t.displayName||"Context")+".Provider";case Ae:return(t._context.displayName||"Context")+".Consumer";case fe:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case be:return e=t.displayName||null,e!==null?e:de(t.type)||"Memo";case xe:e=t._payload,t=t._init;try{return de(t(e))}catch{}}return null}var ae=Array.isArray,D=E.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Q=b.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,P={pending:!1,data:null,method:null,action:null},Mt=[],m=-1;function R(t){return{current:t}}function Y(t){0>m||(t.current=Mt[m],Mt[m]=null,m--)}function G(t,e){m++,Mt[m]=t.current,t.current=e}var J=R(null),$=R(null),X=R(null),Pt=R(null);function xt(t,e){switch(G(X,e),G($,t),G(J,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?Uh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=Uh(e),t=Hh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Y(J),G(J,t)}function mi(){Y(J),Y($),Y(X)}function ma(t){t.memoizedState!==null&&G(Pt,t);var e=J.current,n=Hh(e,t.type);e!==n&&(G($,t),G(J,n))}function yn(t){$.current===t&&(Y(J),Y($)),Pt.current===t&&(Y(Pt),Rs._currentValue=P)}var ki=Object.prototype.hasOwnProperty,_a=g.unstable_scheduleCallback,Bl=g.unstable_cancelCallback,Ps=g.unstable_shouldYield,Gs=g.unstable_requestPaint,Ue=g.unstable_now,Va=g.unstable_getCurrentPriorityLevel,Ys=g.unstable_ImmediatePriority,Zl=g.unstable_UserBlockingPriority,bn=g.unstable_NormalPriority,Vs=g.unstable_LowPriority,jl=g.unstable_IdlePriority,yu=g.log,bu=g.unstable_setDisableYieldValue,Pi=null,Se=null;function _i(t){if(typeof yu=="function"&&bu(t),Se&&typeof Se.setStrictMode=="function")try{Se.setStrictMode(Pi,t)}catch{}}var Oe=Math.clz32?Math.clz32:xu,Xs=Math.log,Qs=Math.LN2;function xu(t){return t>>>=0,t===0?32:31-(Xs(t)/Qs|0)|0}var pa=256,xn=4194304;function Mi(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function Xa(t,e,n){var l=t.pendingLanes;if(l===0)return 0;var o=0,u=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var d=l&134217727;return d!==0?(l=d&~u,l!==0?o=Mi(l):(f&=d,f!==0?o=Mi(f):n||(n=d&~t,n!==0&&(o=Mi(n))))):(d=l&~u,d!==0?o=Mi(d):f!==0?o=Mi(f):n||(n=l&~t,n!==0&&(o=Mi(n)))),o===0?0:e!==0&&e!==o&&(e&u)===0&&(u=o&-o,n=e&-e,u>=n||u===32&&(n&4194048)!==0)?e:o}function pi(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Su(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ks(){var t=pa;return pa<<=1,(pa&4194048)===0&&(pa=256),t}function Ul(){var t=xn;return xn<<=1,(xn&62914560)===0&&(xn=4194304),t}function Qa(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Sn(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Tu(t,e,n,l,o,u){var f=t.pendingLanes;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=n,t.entangledLanes&=n,t.errorRecoveryDisabledLanes&=n,t.shellSuspendCounter=0;var d=t.entanglements,p=t.expirationTimes,M=t.hiddenUpdates;for(n=f&~n;0<n;){var Z=31-Oe(n),U=1<<Z;d[Z]=0,p[Z]=-1;var z=M[Z];if(z!==null)for(M[Z]=null,Z=0;Z<z.length;Z++){var O=z[Z];O!==null&&(O.lane&=-536870913)}n&=~U}l!==0&&Js(t,l,0),u!==0&&o===0&&t.tag!==0&&(t.suspendedLanes|=u&~(f&~e))}function Js(t,e,n){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-Oe(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|n&4194090}function Ws(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var l=31-Oe(n),o=1<<l;o&e|t[l]&e&&(t[l]|=e),n&=~o}}function Hl(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ql(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Fs(){var t=Q.p;return t!==0?t:(t=window.event,t===void 0?32:ad(t.type))}function kl(t,e){var n=Q.p;try{return Q.p=t,e()}finally{Q.p=n}}var Ei=Math.random().toString(36).slice(2),le="__reactFiber$"+Ei,Te="__reactProps$"+Ei,Tn="__reactContainer$"+Ei,He="__reactEvents$"+Ei,et="__reactListeners$"+Ei,Is="__reactHandles$"+Ei,Pl="__reactResources$"+Ei,wn="__reactMarker$"+Ei;function Ka(t){delete t[le],delete t[Te],delete t[He],delete t[et],delete t[Is]}function zi(t){var e=t[le];if(e)return e;for(var n=t.parentNode;n;){if(e=n[Tn]||n[le]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=Gh(t);t!==null;){if(n=t[le])return n;t=Gh(t)}return e}t=n,n=t.parentNode}return null}function Gi(t){if(t=t[le]||t[Tn]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function ni(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(v(33))}function Yi(t){var e=t[Pl];return e||(e=t[Pl]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ft(t){t[wn]=!0}var $s=new Set,to={};function Vi(t,e){Xi(t,e),Xi(t+"Capture",e)}function Xi(t,e){for(to[t]=e,t=0;t<e.length;t++)$s.add(e[t])}var wu=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),va={},eo={};function Mu(t){return ki.call(eo,t)?!0:ki.call(va,t)?!1:wu.test(t)?eo[t]=!0:(va[t]=!0,!1)}function Ja(t,e,n){if(Mu(e))if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+n)}}function Wa(t,e,n){if(n===null)t.removeAttribute(e);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+n)}}function vi(t,e,n,l){if(l===null)t.removeAttribute(n);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(n);return}t.setAttributeNS(e,n,""+l)}}var ga,Mn;function Qi(t){if(ga===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);ga=e&&e[1]||"",Mn=-1<n.stack.indexOf(`
    at`)?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return`
`+ga+t+Mn}var Fa=!1;function Ki(t,e){if(!t||Fa)return"";Fa=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var U=function(){throw Error()};if(Object.defineProperty(U.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(U,[])}catch(O){var z=O}Reflect.construct(t,[],U)}else{try{U.call()}catch(O){z=O}t.call(U.prototype)}}else{try{throw Error()}catch(O){z=O}(U=t())&&typeof U.catch=="function"&&U.catch(function(){})}}catch(O){if(O&&z&&typeof O.stack=="string")return[O.stack,z.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),f=u[0],d=u[1];if(f&&d){var p=f.split(`
`),M=d.split(`
`);for(o=l=0;l<p.length&&!p[l].includes("DetermineComponentFrameRoot");)l++;for(;o<M.length&&!M[o].includes("DetermineComponentFrameRoot");)o++;if(l===p.length||o===M.length)for(l=p.length-1,o=M.length-1;1<=l&&0<=o&&p[l]!==M[o];)o--;for(;1<=l&&0<=o;l--,o--)if(p[l]!==M[o]){if(l!==1||o!==1)do if(l--,o--,0>o||p[l]!==M[o]){var Z=`
`+p[l].replace(" at new "," at ");return t.displayName&&Z.includes("<anonymous>")&&(Z=Z.replace("<anonymous>",t.displayName)),Z}while(1<=l&&0<=o);break}}}finally{Fa=!1,Error.prepareStackTrace=n}return(n=t?t.displayName||t.name:"")?Qi(n):""}function yt(t){switch(t.tag){case 26:case 27:case 5:return Qi(t.type);case 16:return Qi("Lazy");case 13:return Qi("Suspense");case 19:return Qi("SuspenseList");case 0:case 15:return Ki(t.type,!1);case 11:return Ki(t.type.render,!1);case 1:return Ki(t.type,!0);case 31:return Qi("Activity");default:return""}}function Dt(t){try{var e="";do e+=yt(t),t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}function me(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Ji(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function En(t){var e=Ji(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(f){l=""+f,u.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function zn(t){t._valueTracker||(t._valueTracker=En(t))}function rt(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),l="";return t&&(l=Ji(t)?t.checked?"true":"false":t.value),t=l,t!==n?(e.setValue(t),!0):!1}function Rt(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var Gl=/[\n"\\]/g;function _e(t){return t.replace(Gl,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function we(t,e,n,l,o,u,f,d){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+me(e)):t.value!==""+me(e)&&(t.value=""+me(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?Ln(t,f,me(e)):n!=null?Ln(t,f,me(n)):l!=null&&t.removeAttribute("value"),o==null&&u!=null&&(t.defaultChecked=!!u),o!=null&&(t.checked=o&&typeof o!="function"&&typeof o!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+me(d):t.removeAttribute("name")}function io(t,e,n,l,o,u,f,d){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||n!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;n=n!=null?""+me(n):"",e=e!=null?""+me(e):n,d||e===t.value||(t.value=e),t.defaultValue=e}l=l??o,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=d?t.checked:!!l,t.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f)}function Ln(t,e,n){e==="number"&&Rt(t.ownerDocument)===t||t.defaultValue===""+n||(t.defaultValue=""+n)}function qe(t,e,n,l){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&l&&(t[n].defaultSelected=!0)}else{for(n=""+me(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,l&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Gt(t,e,n){if(e!=null&&(e=""+me(e),e!==t.value&&(t.value=e),n==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=n!=null?""+me(n):""}function Li(t,e,n,l){if(e==null){if(l!=null){if(n!=null)throw Error(v(92));if(ae(l)){if(1<l.length)throw Error(v(93));l=l[0]}n=l}n==null&&(n=""),e=n}n=me(e),t.defaultValue=n,l=t.textContent,l===n&&l!==""&&l!==null&&(t.value=l)}function ai(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var ya=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ia(t,e,n){var l=e.indexOf("--")===0;n==null||typeof n=="boolean"||n===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,n):typeof n!="number"||n===0||ya.has(e)?e==="float"?t.cssFloat=n:t[e]=(""+n).trim():t[e]=n+"px"}function An(t,e,n){if(e!=null&&typeof e!="object")throw Error(v(62));if(t=t.style,n!=null){for(var l in n)!n.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var o in e)l=e[o],e.hasOwnProperty(o)&&n[o]!==l&&Ia(t,o,l)}else for(var u in e)e.hasOwnProperty(u)&&Ia(t,u,e[u])}function ba(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Yl=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),$a=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function On(t){return $a.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var xa=null;function Nn(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Wi=null,Ai=null;function no(t){var e=Gi(t);if(e&&(t=e.stateNode)){var n=t[Te]||null;t:switch(t=e.stateNode,e.type){case"input":if(we(t,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+_e(""+e)+'"][type="radio"]'),e=0;e<n.length;e++){var l=n[e];if(l!==t&&l.form===t.form){var o=l[Te]||null;if(!o)throw Error(v(90));we(l,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(e=0;e<n.length;e++)l=n[e],l.form===t.form&&rt(l)}break t;case"textarea":Gt(t,n.value,n.defaultValue);break t;case"select":e=n.value,e!=null&&qe(t,!!n.multiple,e,!1)}}}var ot=!1;function Ve(t,e,n){if(ot)return t(e,n);ot=!0;try{var l=t(e);return l}finally{if(ot=!1,(Wi!==null||Ai!==null)&&(Io(),Wi&&(e=Wi,t=Ai,Ai=Wi=null,no(e),t)))for(e=0;e<t.length;e++)no(t[e])}}function St(t,e){var n=t.stateNode;if(n===null)return null;var l=n[Te]||null;if(l===null)return null;n=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(v(231,e,typeof n));return n}var li=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Sa=!1;if(li)try{var Fi={};Object.defineProperty(Fi,"passive",{get:function(){Sa=!0}}),window.addEventListener("test",Fi,Fi),window.removeEventListener("test",Fi,Fi)}catch{Sa=!1}var si=null,gi=null,Cn=null;function Dn(){if(Cn)return Cn;var t,e=gi,n=e.length,l,o="value"in si?si.value:si.textContent,u=o.length;for(t=0;t<n&&e[t]===o[t];t++);var f=n-t;for(l=1;l<=f&&e[n-l]===o[u-l];l++);return Cn=o.slice(t,1<l?1-l:void 0)}function Qt(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function oi(){return!0}function Vl(){return!1}function pe(t){function e(n,l,o,u,f){this._reactName=n,this._targetInst=o,this.type=l,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(n=t[d],this[d]=n?n(u):u[d]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?oi:Vl,this.isPropagationStopped=Vl,this}return q(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=oi)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=oi)},persist:function(){},isPersistent:oi}),e}var Ii={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ta=pe(Ii),$i=q({},Ii,{view:0,detail:0}),Eu=pe($i),tl,_t,wa,Me=q({},$i,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:el,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==wa&&(wa&&t.type==="mousemove"?(tl=t.screenX-wa.screenX,_t=t.screenY-wa.screenY):_t=tl=0,wa=t),tl)},movementY:function(t){return"movementY"in t?t.movementY:_t}}),Rn=pe(Me),ao=q({},Me,{dataTransfer:0}),zu=pe(ao),Xl=q({},$i,{relatedTarget:0}),Ql=pe(Xl),lo=q({},Ii,{animationName:0,elapsedTime:0,pseudoElement:0}),Lu=pe(lo),Au=q({},Ii,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Kl=pe(Au),Ou=q({},Ii,{data:0}),Xe=pe(Ou),Nu={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},so={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Oi={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function oo(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Oi[t])?!!e[t]:!1}function el(){return oo}var Jl=q({},$i,{key:function(t){if(t.key){var e=Nu[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Qt(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?so[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:el,charCode:function(t){return t.type==="keypress"?Qt(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Qt(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Cu=pe(Jl),uo=q({},Me,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Wl=pe(uo),Du=q({},$i,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:el}),Ru=pe(Du),Fl=q({},Ii,{propertyName:0,elapsedTime:0,pseudoElement:0}),Bu=pe(Fl),ro=q({},Me,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),co=pe(ro),il=q({},Ii,{newState:0,oldState:0}),tn=pe(il),Zu=[9,13,27,32],en=li&&"CompositionEvent"in window,se=null;li&&"documentMode"in document&&(se=document.documentMode);var fo=li&&"TextEvent"in window&&!se,Il=li&&(!en||se&&8<se&&11>=se),ho=" ",nl=!1;function al(t,e){switch(t){case"keyup":return Zu.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function mo(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Bn=!1;function _o(t,e){switch(t){case"compositionend":return mo(e);case"keypress":return e.which!==32?null:(nl=!0,ho);case"textInput":return t=e.data,t===ho&&nl?null:t;default:return null}}function ju(t,e){if(Bn)return t==="compositionend"||!en&&al(t,e)?(t=Dn(),Cn=gi=si=null,Bn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return Il&&e.locale!=="ko"?null:e.data;default:return null}}var Qe={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function nn(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!Qe[t.type]:e==="textarea"}function po(t,e,n,l){Wi?Ai?Ai.push(l):Ai=[l]:Wi=l,e=au(e,"onChange"),0<e.length&&(n=new Ta("onChange","change",null,n,l),t.push({event:n,listeners:e}))}var Ne=null,Ma=null;function Zn(t){Dh(t,0)}function ll(t){var e=ni(t);if(rt(e))return t}function jn(t,e){if(t==="change")return e}var $l=!1;if(li){var Un;if(li){var ts="oninput"in document;if(!ts){var yi=document.createElement("div");yi.setAttribute("oninput","return;"),ts=typeof yi.oninput=="function"}Un=ts}else Un=!1;$l=Un&&(!document.documentMode||9<document.documentMode)}function Ea(){Ne&&(Ne.detachEvent("onpropertychange",vo),Ma=Ne=null)}function vo(t){if(t.propertyName==="value"&&ll(Ma)){var e=[];po(e,Ma,t,Nn(t)),Ve(Zn,e)}}function es(t,e,n){t==="focusin"?(Ea(),Ne=e,Ma=n,Ne.attachEvent("onpropertychange",vo)):t==="focusout"&&Ea()}function Uu(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return ll(Ma)}function bi(t,e){if(t==="click")return ll(e)}function Hu(t,e){if(t==="input"||t==="change")return ll(e)}function Hn(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ce=typeof Object.is=="function"?Object.is:Hn;function De(t,e){if(Ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),l=Object.keys(e);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var o=n[l];if(!ki.call(e,o)||!Ce(t[o],e[o]))return!1}return!0}function za(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function is(t,e){var n=za(t);t=0;for(var l;n;){if(n.nodeType===3){if(l=t+n.textContent.length,t<=e&&l>=e)return{node:n,offset:e-t};t=l}t:{for(;n;){if(n.nextSibling){n=n.nextSibling;break t}n=n.parentNode}n=void 0}n=za(n)}}function sl(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?sl(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function La(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Rt(t.document);e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch{n=!1}if(n)t=e.contentWindow;else break;e=Rt(t.document)}return e}function Aa(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var ol=li&&"documentMode"in document&&11>=document.documentMode,Ke=null,qn=null,an=null,ul=!1;function go(t,e,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ul||Ke==null||Ke!==Rt(l)||(l=Ke,"selectionStart"in l&&Aa(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),an&&De(an,l)||(an=l,l=au(qn,"onSelect"),0<l.length&&(e=new Ta("onSelect","select",null,e,n),t.push({event:e,listeners:l}),e.target=Ke)))}function ui(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var kn={animationend:ui("Animation","AnimationEnd"),animationiteration:ui("Animation","AnimationIteration"),animationstart:ui("Animation","AnimationStart"),transitionrun:ui("Transition","TransitionRun"),transitionstart:ui("Transition","TransitionStart"),transitioncancel:ui("Transition","TransitionCancel"),transitionend:ui("Transition","TransitionEnd")},rl={},yo={};li&&(yo=document.createElement("div").style,"AnimationEvent"in window||(delete kn.animationend.animation,delete kn.animationiteration.animation,delete kn.animationstart.animation),"TransitionEvent"in window||delete kn.transitionend.transition);function Ni(t){if(rl[t])return rl[t];if(!kn[t])return t;var e=kn[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in yo)return rl[t]=e[n];return t}var bo=Ni("animationend"),Je=Ni("animationiteration"),Oa=Ni("animationstart"),qu=Ni("transitionrun"),cl=Ni("transitionstart"),ku=Ni("transitioncancel"),ns=Ni("transitionend"),xo=new Map,ln="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");ln.push("scrollEnd");function We(t,e){xo.set(t,e),Vi(e,[t])}var sn=new WeakMap;function Re(t,e){if(typeof t=="object"&&t!==null){var n=sn.get(t);return n!==void 0?n:(e={value:t,source:e,stack:Dt(e)},sn.set(t,e),e)}return{value:t,source:e,stack:Dt(e)}}var Be=[],Pn=0,Fe=0;function Na(){for(var t=Pn,e=Fe=Pn=0;e<t;){var n=Be[e];Be[e++]=null;var l=Be[e];Be[e++]=null;var o=Be[e];Be[e++]=null;var u=Be[e];if(Be[e++]=null,l!==null&&o!==null){var f=l.pending;f===null?o.next=o:(o.next=f.next,f.next=o),l.pending=o}u!==0&&Da(n,o,u)}}function Ca(t,e,n,l){Be[Pn++]=t,Be[Pn++]=e,Be[Pn++]=n,Be[Pn++]=l,Fe|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function on(t,e,n,l){return Ca(t,e,n,l),Ci(t)}function Gn(t,e){return Ca(t,null,null,e),Ci(t)}function Da(t,e,n){t.lanes|=n;var l=t.alternate;l!==null&&(l.lanes|=n);for(var o=!1,u=t.return;u!==null;)u.childLanes|=n,l=u.alternate,l!==null&&(l.childLanes|=n),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(o=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,o&&e!==null&&(o=31-Oe(n),t=u.hiddenUpdates,l=t[o],l===null?t[o]=[e]:l.push(e),e.lane=n|536870912),u):null}function Ci(t){if(50<Es)throw Es=0,Ur=null,Error(v(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var un={};function So(t,e,n,l){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ze(t,e,n,l){return new So(t,e,n,l)}function fl(t){return t=t.prototype,!(!t||!t.isReactComponent)}function ri(t,e){var n=t.alternate;return n===null?(n=Ze(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&65011712,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n.refCleanup=t.refCleanup,n}function as(t,e){t.flags&=65011714;var n=t.alternate;return n===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=n.childLanes,t.lanes=n.lanes,t.child=n.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=n.memoizedProps,t.memoizedState=n.memoizedState,t.updateQueue=n.updateQueue,t.type=n.type,e=n.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ra(t,e,n,l,o,u){var f=0;if(l=t,typeof t=="function")fl(t)&&(f=1);else if(typeof t=="string")f=Cm(t,n,J.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case je:return t=Ze(31,n,e,o),t.elementType=je,t.lanes=u,t;case jt:return Di(n.children,o,u,e);case mt:f=8,o|=24;break;case Xt:return t=Ze(12,n,e,o|2),t.elementType=Xt,t.lanes=u,t;case ht:return t=Ze(13,n,e,o),t.elementType=ht,t.lanes=u,t;case he:return t=Ze(19,n,e,o),t.elementType=he,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case ne:case wt:f=10;break t;case Ae:f=9;break t;case fe:f=11;break t;case be:f=14;break t;case xe:f=16,l=null;break t}f=29,n=Error(v(130,t===null?"null":typeof t,"")),l=null}return e=Ze(f,n,e,o),e.elementType=t,e.type=l,e.lanes=u,e}function Di(t,e,n,l){return t=Ze(7,t,l,e),t.lanes=n,t}function ls(t,e,n){return t=Ze(6,t,null,e),t.lanes=n,t}function hl(t,e,n){return e=Ze(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var rn=[],Yn=0,i=null,a=0,s=[],r=0,c=null,h=1,_="";function T(t,e){rn[Yn++]=a,rn[Yn++]=i,i=t,a=e}function N(t,e,n){s[r++]=h,s[r++]=_,s[r++]=c,c=t;var l=h;t=_;var o=32-Oe(l)-1;l&=~(1<<o),n+=1;var u=32-Oe(e)+o;if(30<u){var f=o-o%5;u=(l&(1<<f)-1).toString(32),l>>=f,o-=f,h=1<<32-Oe(e)+o|n<<o|l,_=u+t}else h=1<<u|n<<o|l,_=t}function H(t){t.return!==null&&(T(t,1),N(t,1,0))}function K(t){for(;t===i;)i=rn[--Yn],rn[Yn]=null,a=rn[--Yn],rn[Yn]=null;for(;t===c;)c=s[--r],s[r]=null,_=s[--r],s[r]=null,h=s[--r],s[r]=null}var W=null,tt=null,st=!1,Bt=null,Yt=!1,oe=Error(v(519));function ke(t){var e=Error(v(418,""));throw Xn(Re(e,t)),oe}function To(t){var e=t.stateNode,n=t.type,l=t.memoizedProps;switch(e[le]=t,e[Te]=l,n){case"dialog":gt("cancel",e),gt("close",e);break;case"iframe":case"object":case"embed":gt("load",e);break;case"video":case"audio":for(n=0;n<Ls.length;n++)gt(Ls[n],e);break;case"source":gt("error",e);break;case"img":case"image":case"link":gt("error",e),gt("load",e);break;case"details":gt("toggle",e);break;case"input":gt("invalid",e),io(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),zn(e);break;case"select":gt("invalid",e);break;case"textarea":gt("invalid",e),Li(e,l.value,l.defaultValue,l.children),zn(e)}n=l.children,typeof n!="string"&&typeof n!="number"&&typeof n!="bigint"||e.textContent===""+n||l.suppressHydrationWarning===!0||jh(e.textContent,n)?(l.popover!=null&&(gt("beforetoggle",e),gt("toggle",e)),l.onScroll!=null&&gt("scroll",e),l.onScrollEnd!=null&&gt("scrollend",e),l.onClick!=null&&(e.onclick=lu),e=!0):e=!1,e||ke(t)}function wo(t){for(W=t.return;W;)switch(W.tag){case 5:case 13:Yt=!1;return;case 27:case 3:Yt=!0;return;default:W=W.return}}function Ba(t){if(t!==W)return!1;if(!st)return wo(t),st=!0,!1;var e=t.tag,n;if((n=e!==3&&e!==27)&&((n=e===5)&&(n=t.type,n=!(n!=="form"&&n!=="button")||tc(t.type,t.memoizedProps)),n=!n),n&&tt&&ke(t),wo(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(v(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(n=t.data,n==="/$"){if(e===0){tt=wi(t.nextSibling);break t}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++;t=t.nextSibling}tt=null}}else e===27?(e=tt,oa(t.type)?(t=ac,ac=null,tt=t):tt=e):tt=W?wi(t.stateNode.nextSibling):null;return!0}function Vn(){tt=W=null,st=!1}function Mo(){var t=Bt;return t!==null&&(Ye===null?Ye=t:Ye.push.apply(Ye,t),Bt=null),t}function Xn(t){Bt===null?Bt=[t]:Bt.push(t)}var Ht=R(null),ci=null,xi=null;function Ri(t,e,n){G(Ht,e._currentValue),e._currentValue=n}function Si(t){t._currentValue=Ht.current,Y(Ht)}function Za(t,e,n){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===n)break;t=t.return}}function dl(t,e,n,l){var o=t.child;for(o!==null&&(o.return=t);o!==null;){var u=o.dependencies;if(u!==null){var f=o.child;u=u.firstContext;t:for(;u!==null;){var d=u;u=o;for(var p=0;p<e.length;p++)if(d.context===e[p]){u.lanes|=n,d=u.alternate,d!==null&&(d.lanes|=n),Za(u.return,n,t),l||(f=null);break t}u=d.next}}else if(o.tag===18){if(f=o.return,f===null)throw Error(v(341));f.lanes|=n,u=f.alternate,u!==null&&(u.lanes|=n),Za(f,n,t),f=null}else f=o.child;if(f!==null)f.return=o;else for(f=o;f!==null;){if(f===t){f=null;break}if(o=f.sibling,o!==null){o.return=f.return,f=o;break}f=f.return}o=f}}function ja(t,e,n,l){t=null;for(var o=e,u=!1;o!==null;){if(!u){if((o.flags&524288)!==0)u=!0;else if((o.flags&262144)!==0)break}if(o.tag===10){var f=o.alternate;if(f===null)throw Error(v(387));if(f=f.memoizedProps,f!==null){var d=o.type;Ce(o.pendingProps.value,f.value)||(t!==null?t.push(d):t=[d])}}else if(o===Pt.current){if(f=o.alternate,f===null)throw Error(v(387));f.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(t!==null?t.push(Rs):t=[Rs])}o=o.return}t!==null&&dl(e,t,n,l),e.flags|=262144}function Eo(t){for(t=t.firstContext;t!==null;){if(!Ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Ua(t){ci=t,xi=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Ee(t){return Sc(ci,t)}function zo(t,e){return ci===null&&Ua(t),Sc(t,e)}function Sc(t,e){var n=e._currentValue;if(e={context:e,memoizedValue:n,next:null},xi===null){if(t===null)throw Error(v(308));xi=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else xi=xi.next=e;return n}var Nd=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(n,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(n){return n()})}},Cd=g.unstable_scheduleCallback,Dd=g.unstable_NormalPriority,te={$$typeof:wt,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Pu(){return{controller:new Nd,data:new Map,refCount:0}}function ss(t){t.refCount--,t.refCount===0&&Cd(Dd,function(){t.controller.abort()})}var os=null,Gu=0,ml=0,_l=null;function Rd(t,e){if(os===null){var n=os=[];Gu=0,ml=Vr(),_l={status:"pending",value:void 0,then:function(l){n.push(l)}}}return Gu++,e.then(Tc,Tc),e}function Tc(){if(--Gu===0&&os!==null){_l!==null&&(_l.status="fulfilled");var t=os;os=null,ml=0,_l=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function Bd(t,e){var n=[],l={status:"pending",value:null,reason:null,then:function(o){n.push(o)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var o=0;o<n.length;o++)(0,n[o])(e)},function(o){for(l.status="rejected",l.reason=o,o=0;o<n.length;o++)(0,n[o])(void 0)}),l}var wc=D.S;D.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Rd(t,e),wc!==null&&wc(t,e)};var Ha=R(null);function Yu(){var t=Ha.current;return t!==null?t:Zt.pooledCache}function Lo(t,e){e===null?G(Ha,Ha.current):G(Ha,e.pool)}function Mc(){var t=Yu();return t===null?null:{parent:te._currentValue,pool:t}}var us=Error(v(460)),Ec=Error(v(474)),Ao=Error(v(542)),Vu={then:function(){}};function zc(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Oo(){}function Lc(t,e,n){switch(n=t[n],n===void 0?t.push(e):n!==e&&(e.then(Oo,Oo),e=n),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Oc(t),t;default:if(typeof e.status=="string")e.then(Oo,Oo);else{if(t=Zt,t!==null&&100<t.shellSuspendCounter)throw Error(v(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var o=e;o.status="fulfilled",o.value=l}},function(l){if(e.status==="pending"){var o=e;o.status="rejected",o.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Oc(t),t}throw rs=e,us}}var rs=null;function Ac(){if(rs===null)throw Error(v(459));var t=rs;return rs=null,t}function Oc(t){if(t===us||t===Ao)throw Error(v(483))}var Qn=!1;function Xu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Qu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Kn(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Jn(t,e,n){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(zt&2)!==0){var o=l.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),l.pending=e,e=Ci(t),Da(t,null,n),e}return Ca(t,l,e,n),Ci(t)}function cs(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ws(t,n)}}function Ku(t,e){var n=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var o=null,u=null;if(n=n.firstBaseUpdate,n!==null){do{var f={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};u===null?o=u=f:u=u.next=f,n=n.next}while(n!==null);u===null?o=u=e:u=u.next=e}else o=u=e;n={baseState:l.baseState,firstBaseUpdate:o,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}var Ju=!1;function fs(){if(Ju){var t=_l;if(t!==null)throw t}}function hs(t,e,n,l){Ju=!1;var o=t.updateQueue;Qn=!1;var u=o.firstBaseUpdate,f=o.lastBaseUpdate,d=o.shared.pending;if(d!==null){o.shared.pending=null;var p=d,M=p.next;p.next=null,f===null?u=M:f.next=M,f=p;var Z=t.alternate;Z!==null&&(Z=Z.updateQueue,d=Z.lastBaseUpdate,d!==f&&(d===null?Z.firstBaseUpdate=M:d.next=M,Z.lastBaseUpdate=p))}if(u!==null){var U=o.baseState;f=0,Z=M=p=null,d=u;do{var z=d.lane&-536870913,O=z!==d.lane;if(O?(bt&z)===z:(l&z)===z){z!==0&&z===ml&&(Ju=!0),Z!==null&&(Z=Z.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var lt=t,nt=d;z=e;var Nt=n;switch(nt.tag){case 1:if(lt=nt.payload,typeof lt=="function"){U=lt.call(Nt,U,z);break t}U=lt;break t;case 3:lt.flags=lt.flags&-65537|128;case 0:if(lt=nt.payload,z=typeof lt=="function"?lt.call(Nt,U,z):lt,z==null)break t;U=q({},U,z);break t;case 2:Qn=!0}}z=d.callback,z!==null&&(t.flags|=64,O&&(t.flags|=8192),O=o.callbacks,O===null?o.callbacks=[z]:O.push(z))}else O={lane:z,tag:d.tag,payload:d.payload,callback:d.callback,next:null},Z===null?(M=Z=O,p=U):Z=Z.next=O,f|=z;if(d=d.next,d===null){if(d=o.shared.pending,d===null)break;O=d,d=O.next,O.next=null,o.lastBaseUpdate=O,o.shared.pending=null}}while(!0);Z===null&&(p=U),o.baseState=p,o.firstBaseUpdate=M,o.lastBaseUpdate=Z,u===null&&(o.shared.lanes=0),na|=f,t.lanes=f,t.memoizedState=U}}function Nc(t,e){if(typeof t!="function")throw Error(v(191,t));t.call(e)}function Cc(t,e){var n=t.callbacks;if(n!==null)for(t.callbacks=null,t=0;t<n.length;t++)Nc(n[t],e)}var pl=R(null),No=R(0);function Dc(t,e){t=pn,G(No,t),G(pl,e),pn=t|e.baseLanes}function Wu(){G(No,pn),G(pl,pl.current)}function Fu(){pn=No.current,Y(pl),Y(No)}var Wn=0,dt=null,At=null,It=null,Co=!1,vl=!1,qa=!1,Do=0,ds=0,gl=null,Zd=0;function Jt(){throw Error(v(321))}function Iu(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Ce(t[n],e[n]))return!1;return!0}function $u(t,e,n,l,o,u){return Wn=u,dt=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,D.H=t===null||t.memoizedState===null?vf:gf,qa=!1,u=n(l,o),qa=!1,vl&&(u=Bc(e,n,l,o)),Rc(t),u}function Rc(t){D.H=Ho;var e=At!==null&&At.next!==null;if(Wn=0,It=At=dt=null,Co=!1,ds=0,gl=null,e)throw Error(v(300));t===null||ue||(t=t.dependencies,t!==null&&Eo(t)&&(ue=!0))}function Bc(t,e,n,l){dt=t;var o=0;do{if(vl&&(gl=null),ds=0,vl=!1,25<=o)throw Error(v(301));if(o+=1,It=At=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}D.H=Gd,u=e(n,l)}while(vl);return u}function jd(){var t=D.H,e=t.useState()[0];return e=typeof e.then=="function"?ms(e):e,t=t.useState()[0],(At!==null?At.memoizedState:null)!==t&&(dt.flags|=1024),e}function tr(){var t=Do!==0;return Do=0,t}function er(t,e,n){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~n}function ir(t){if(Co){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Co=!1}Wn=0,It=At=dt=null,vl=!1,ds=Do=0,gl=null}function Pe(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return It===null?dt.memoizedState=It=t:It=It.next=t,It}function $t(){if(At===null){var t=dt.alternate;t=t!==null?t.memoizedState:null}else t=At.next;var e=It===null?dt.memoizedState:It.next;if(e!==null)It=e,At=t;else{if(t===null)throw dt.alternate===null?Error(v(467)):Error(v(310));At=t,t={memoizedState:At.memoizedState,baseState:At.baseState,baseQueue:At.baseQueue,queue:At.queue,next:null},It===null?dt.memoizedState=It=t:It=It.next=t}return It}function nr(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ms(t){var e=ds;return ds+=1,gl===null&&(gl=[]),t=Lc(gl,t,e),e=dt,(It===null?e.memoizedState:It.next)===null&&(e=e.alternate,D.H=e===null||e.memoizedState===null?vf:gf),t}function Ro(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return ms(t);if(t.$$typeof===wt)return Ee(t)}throw Error(v(438,String(t)))}function ar(t){var e=null,n=dt.updateQueue;if(n!==null&&(e=n.memoCache),e==null){var l=dt.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(o){return o.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),n===null&&(n=nr(),dt.updateQueue=n),n.memoCache=e,n=e.data[e.index],n===void 0)for(n=e.data[e.index]=Array(t),l=0;l<t;l++)n[l]=Hi;return e.index++,n}function cn(t,e){return typeof e=="function"?e(t):e}function Bo(t){var e=$t();return lr(e,At,t)}function lr(t,e,n){var l=t.queue;if(l===null)throw Error(v(311));l.lastRenderedReducer=n;var o=t.baseQueue,u=l.pending;if(u!==null){if(o!==null){var f=o.next;o.next=u.next,u.next=f}e.baseQueue=o=u,l.pending=null}if(u=t.baseState,o===null)t.memoizedState=u;else{e=o.next;var d=f=null,p=null,M=e,Z=!1;do{var U=M.lane&-536870913;if(U!==M.lane?(bt&U)===U:(Wn&U)===U){var z=M.revertLane;if(z===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),U===ml&&(Z=!0);else if((Wn&z)===z){M=M.next,z===ml&&(Z=!0);continue}else U={lane:0,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},p===null?(d=p=U,f=u):p=p.next=U,dt.lanes|=z,na|=z;U=M.action,qa&&n(u,U),u=M.hasEagerState?M.eagerState:n(u,U)}else z={lane:U,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},p===null?(d=p=z,f=u):p=p.next=z,dt.lanes|=U,na|=U;M=M.next}while(M!==null&&M!==e);if(p===null?f=u:p.next=d,!Ce(u,t.memoizedState)&&(ue=!0,Z&&(n=_l,n!==null)))throw n;t.memoizedState=u,t.baseState=f,t.baseQueue=p,l.lastRenderedState=u}return o===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function sr(t){var e=$t(),n=e.queue;if(n===null)throw Error(v(311));n.lastRenderedReducer=t;var l=n.dispatch,o=n.pending,u=e.memoizedState;if(o!==null){n.pending=null;var f=o=o.next;do u=t(u,f.action),f=f.next;while(f!==o);Ce(u,e.memoizedState)||(ue=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),n.lastRenderedState=u}return[u,l]}function Zc(t,e,n){var l=dt,o=$t(),u=st;if(u){if(n===void 0)throw Error(v(407));n=n()}else n=e();var f=!Ce((At||o).memoizedState,n);f&&(o.memoizedState=n,ue=!0),o=o.queue;var d=Hc.bind(null,l,o,t);if(_s(2048,8,d,[t]),o.getSnapshot!==e||f||It!==null&&It.memoizedState.tag&1){if(l.flags|=2048,yl(9,Zo(),Uc.bind(null,l,o,n,e),null),Zt===null)throw Error(v(349));u||(Wn&124)!==0||jc(l,e,n)}return n}function jc(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=dt.updateQueue,e===null?(e=nr(),dt.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function Uc(t,e,n,l){e.value=n,e.getSnapshot=l,qc(e)&&kc(t)}function Hc(t,e,n){return n(function(){qc(e)&&kc(t)})}function qc(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Ce(t,n)}catch{return!0}}function kc(t){var e=Gn(t,2);e!==null&&ii(e,t,2)}function or(t){var e=Pe();if(typeof t=="function"){var n=t;if(t=n(),qa){_i(!0);try{n()}finally{_i(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:t},e}function Pc(t,e,n,l){return t.baseState=n,lr(t,At,typeof l=="function"?l:cn)}function Ud(t,e,n,l,o){if(Uo(t))throw Error(v(485));if(t=e.action,t!==null){var u={payload:o,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};D.T!==null?n(!0):u.isTransition=!1,l(u),n=e.pending,n===null?(u.next=e.pending=u,Gc(e,u)):(u.next=n.next,e.pending=n.next=u)}}function Gc(t,e){var n=e.action,l=e.payload,o=t.state;if(e.isTransition){var u=D.T,f={};D.T=f;try{var d=n(o,l),p=D.S;p!==null&&p(f,d),Yc(t,e,d)}catch(M){ur(t,e,M)}finally{D.T=u}}else try{u=n(o,l),Yc(t,e,u)}catch(M){ur(t,e,M)}}function Yc(t,e,n){n!==null&&typeof n=="object"&&typeof n.then=="function"?n.then(function(l){Vc(t,e,l)},function(l){return ur(t,e,l)}):Vc(t,e,n)}function Vc(t,e,n){e.status="fulfilled",e.value=n,Xc(e),t.state=n,e=t.pending,e!==null&&(n=e.next,n===e?t.pending=null:(n=n.next,e.next=n,Gc(t,n)))}function ur(t,e,n){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=n,Xc(e),e=e.next;while(e!==l)}t.action=null}function Xc(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function Qc(t,e){return e}function Kc(t,e){if(st){var n=Zt.formState;if(n!==null){t:{var l=dt;if(st){if(tt){e:{for(var o=tt,u=Yt;o.nodeType!==8;){if(!u){o=null;break e}if(o=wi(o.nextSibling),o===null){o=null;break e}}u=o.data,o=u==="F!"||u==="F"?o:null}if(o){tt=wi(o.nextSibling),l=o.data==="F!";break t}}ke(l)}l=!1}l&&(e=n[0])}}return n=Pe(),n.memoizedState=n.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Qc,lastRenderedState:e},n.queue=l,n=mf.bind(null,dt,l),l.dispatch=n,l=or(!1),u=dr.bind(null,dt,!1,l.queue),l=Pe(),o={state:e,dispatch:null,action:t,pending:null},l.queue=o,n=Ud.bind(null,dt,o,u,n),o.dispatch=n,l.memoizedState=t,[e,n,!1]}function Jc(t){var e=$t();return Wc(e,At,t)}function Wc(t,e,n){if(e=lr(t,e,Qc)[0],t=Bo(cn)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=ms(e)}catch(f){throw f===us?Ao:f}else l=e;e=$t();var o=e.queue,u=o.dispatch;return n!==e.memoizedState&&(dt.flags|=2048,yl(9,Zo(),Hd.bind(null,o,n),null)),[l,u,t]}function Hd(t,e){t.action=e}function Fc(t){var e=$t(),n=At;if(n!==null)return Wc(e,n,t);$t(),e=e.memoizedState,n=$t();var l=n.queue.dispatch;return n.memoizedState=t,[e,l,!1]}function yl(t,e,n,l){return t={tag:t,create:n,deps:l,inst:e,next:null},e=dt.updateQueue,e===null&&(e=nr(),dt.updateQueue=e),n=e.lastEffect,n===null?e.lastEffect=t.next=t:(l=n.next,n.next=t,t.next=l,e.lastEffect=t),t}function Zo(){return{destroy:void 0,resource:void 0}}function Ic(){return $t().memoizedState}function jo(t,e,n,l){var o=Pe();l=l===void 0?null:l,dt.flags|=t,o.memoizedState=yl(1|e,Zo(),n,l)}function _s(t,e,n,l){var o=$t();l=l===void 0?null:l;var u=o.memoizedState.inst;At!==null&&l!==null&&Iu(l,At.memoizedState.deps)?o.memoizedState=yl(e,u,n,l):(dt.flags|=t,o.memoizedState=yl(1|e,u,n,l))}function $c(t,e){jo(8390656,8,t,e)}function tf(t,e){_s(2048,8,t,e)}function ef(t,e){return _s(4,2,t,e)}function nf(t,e){return _s(4,4,t,e)}function af(t,e){if(typeof e=="function"){t=t();var n=e(t);return function(){typeof n=="function"?n():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function lf(t,e,n){n=n!=null?n.concat([t]):null,_s(4,4,af.bind(null,e,t),n)}function rr(){}function sf(t,e){var n=$t();e=e===void 0?null:e;var l=n.memoizedState;return e!==null&&Iu(e,l[1])?l[0]:(n.memoizedState=[t,e],t)}function of(t,e){var n=$t();e=e===void 0?null:e;var l=n.memoizedState;if(e!==null&&Iu(e,l[1]))return l[0];if(l=t(),qa){_i(!0);try{t()}finally{_i(!1)}}return n.memoizedState=[l,e],l}function cr(t,e,n){return n===void 0||(Wn&1073741824)!==0?t.memoizedState=e:(t.memoizedState=n,t=ch(),dt.lanes|=t,na|=t,n)}function uf(t,e,n,l){return Ce(n,e)?n:pl.current!==null?(t=cr(t,n,l),Ce(t,e)||(ue=!0),t):(Wn&42)===0?(ue=!0,t.memoizedState=n):(t=ch(),dt.lanes|=t,na|=t,e)}function rf(t,e,n,l,o){var u=Q.p;Q.p=u!==0&&8>u?u:8;var f=D.T,d={};D.T=d,dr(t,!1,e,n);try{var p=o(),M=D.S;if(M!==null&&M(d,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var Z=Bd(p,l);ps(t,e,Z,ei(t))}else ps(t,e,l,ei(t))}catch(U){ps(t,e,{then:function(){},status:"rejected",reason:U},ei())}finally{Q.p=u,D.T=f}}function qd(){}function fr(t,e,n,l){if(t.tag!==5)throw Error(v(476));var o=cf(t).queue;rf(t,o,e,P,n===null?qd:function(){return ff(t),n(l)})}function cf(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:P,baseState:P,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:P},next:null};var n={};return e.next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:cn,lastRenderedState:n},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function ff(t){var e=cf(t).next.queue;ps(t,e,{},ei())}function hr(){return Ee(Rs)}function hf(){return $t().memoizedState}function df(){return $t().memoizedState}function kd(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var n=ei();t=Kn(n);var l=Jn(e,t,n);l!==null&&(ii(l,e,n),cs(l,e,n)),e={cache:Pu()},t.payload=e;return}e=e.return}}function Pd(t,e,n){var l=ei();n={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Uo(t)?_f(e,n):(n=on(t,e,n,l),n!==null&&(ii(n,t,l),pf(n,e,l)))}function mf(t,e,n){var l=ei();ps(t,e,n,l)}function ps(t,e,n,l){var o={lane:l,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Uo(t))_f(e,o);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var f=e.lastRenderedState,d=u(f,n);if(o.hasEagerState=!0,o.eagerState=d,Ce(d,f))return Ca(t,e,o,0),Zt===null&&Na(),!1}catch{}finally{}if(n=on(t,e,o,l),n!==null)return ii(n,t,l),pf(n,e,l),!0}return!1}function dr(t,e,n,l){if(l={lane:2,revertLane:Vr(),action:l,hasEagerState:!1,eagerState:null,next:null},Uo(t)){if(e)throw Error(v(479))}else e=on(t,n,l,2),e!==null&&ii(e,t,2)}function Uo(t){var e=t.alternate;return t===dt||e!==null&&e===dt}function _f(t,e){vl=Co=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function pf(t,e,n){if((n&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,n|=l,e.lanes=n,Ws(t,n)}}var Ho={readContext:Ee,use:Ro,useCallback:Jt,useContext:Jt,useEffect:Jt,useImperativeHandle:Jt,useLayoutEffect:Jt,useInsertionEffect:Jt,useMemo:Jt,useReducer:Jt,useRef:Jt,useState:Jt,useDebugValue:Jt,useDeferredValue:Jt,useTransition:Jt,useSyncExternalStore:Jt,useId:Jt,useHostTransitionStatus:Jt,useFormState:Jt,useActionState:Jt,useOptimistic:Jt,useMemoCache:Jt,useCacheRefresh:Jt},vf={readContext:Ee,use:Ro,useCallback:function(t,e){return Pe().memoizedState=[t,e===void 0?null:e],t},useContext:Ee,useEffect:$c,useImperativeHandle:function(t,e,n){n=n!=null?n.concat([t]):null,jo(4194308,4,af.bind(null,e,t),n)},useLayoutEffect:function(t,e){return jo(4194308,4,t,e)},useInsertionEffect:function(t,e){jo(4,2,t,e)},useMemo:function(t,e){var n=Pe();e=e===void 0?null:e;var l=t();if(qa){_i(!0);try{t()}finally{_i(!1)}}return n.memoizedState=[l,e],l},useReducer:function(t,e,n){var l=Pe();if(n!==void 0){var o=n(e);if(qa){_i(!0);try{n(e)}finally{_i(!1)}}}else o=e;return l.memoizedState=l.baseState=o,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},l.queue=t,t=t.dispatch=Pd.bind(null,dt,t),[l.memoizedState,t]},useRef:function(t){var e=Pe();return t={current:t},e.memoizedState=t},useState:function(t){t=or(t);var e=t.queue,n=mf.bind(null,dt,e);return e.dispatch=n,[t.memoizedState,n]},useDebugValue:rr,useDeferredValue:function(t,e){var n=Pe();return cr(n,t,e)},useTransition:function(){var t=or(!1);return t=rf.bind(null,dt,t.queue,!0,!1),Pe().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,n){var l=dt,o=Pe();if(st){if(n===void 0)throw Error(v(407));n=n()}else{if(n=e(),Zt===null)throw Error(v(349));(bt&124)!==0||jc(l,e,n)}o.memoizedState=n;var u={value:n,getSnapshot:e};return o.queue=u,$c(Hc.bind(null,l,u,t),[t]),l.flags|=2048,yl(9,Zo(),Uc.bind(null,l,u,n,e),null),n},useId:function(){var t=Pe(),e=Zt.identifierPrefix;if(st){var n=_,l=h;n=(l&~(1<<32-Oe(l)-1)).toString(32)+n,e="«"+e+"R"+n,n=Do++,0<n&&(e+="H"+n.toString(32)),e+="»"}else n=Zd++,e="«"+e+"r"+n.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:hr,useFormState:Kc,useActionState:Kc,useOptimistic:function(t){var e=Pe();e.memoizedState=e.baseState=t;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=n,e=dr.bind(null,dt,!0,n),n.dispatch=e,[t,e]},useMemoCache:ar,useCacheRefresh:function(){return Pe().memoizedState=kd.bind(null,dt)}},gf={readContext:Ee,use:Ro,useCallback:sf,useContext:Ee,useEffect:tf,useImperativeHandle:lf,useInsertionEffect:ef,useLayoutEffect:nf,useMemo:of,useReducer:Bo,useRef:Ic,useState:function(){return Bo(cn)},useDebugValue:rr,useDeferredValue:function(t,e){var n=$t();return uf(n,At.memoizedState,t,e)},useTransition:function(){var t=Bo(cn)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:ms(t),e]},useSyncExternalStore:Zc,useId:hf,useHostTransitionStatus:hr,useFormState:Jc,useActionState:Jc,useOptimistic:function(t,e){var n=$t();return Pc(n,At,t,e)},useMemoCache:ar,useCacheRefresh:df},Gd={readContext:Ee,use:Ro,useCallback:sf,useContext:Ee,useEffect:tf,useImperativeHandle:lf,useInsertionEffect:ef,useLayoutEffect:nf,useMemo:of,useReducer:sr,useRef:Ic,useState:function(){return sr(cn)},useDebugValue:rr,useDeferredValue:function(t,e){var n=$t();return At===null?cr(n,t,e):uf(n,At.memoizedState,t,e)},useTransition:function(){var t=sr(cn)[0],e=$t().memoizedState;return[typeof t=="boolean"?t:ms(t),e]},useSyncExternalStore:Zc,useId:hf,useHostTransitionStatus:hr,useFormState:Fc,useActionState:Fc,useOptimistic:function(t,e){var n=$t();return At!==null?Pc(n,At,t,e):(n.baseState=t,[t,n.queue.dispatch])},useMemoCache:ar,useCacheRefresh:df},bl=null,vs=0;function qo(t){var e=vs;return vs+=1,bl===null&&(bl=[]),Lc(bl,t,e)}function gs(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function ko(t,e){throw e.$$typeof===I?Error(v(525)):(t=Object.prototype.toString.call(e),Error(v(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function yf(t){var e=t._init;return e(t._payload)}function bf(t){function e(S,y){if(t){var w=S.deletions;w===null?(S.deletions=[y],S.flags|=16):w.push(y)}}function n(S,y){if(!t)return null;for(;y!==null;)e(S,y),y=y.sibling;return null}function l(S){for(var y=new Map;S!==null;)S.key!==null?y.set(S.key,S):y.set(S.index,S),S=S.sibling;return y}function o(S,y){return S=ri(S,y),S.index=0,S.sibling=null,S}function u(S,y,w){return S.index=w,t?(w=S.alternate,w!==null?(w=w.index,w<y?(S.flags|=67108866,y):w):(S.flags|=67108866,y)):(S.flags|=1048576,y)}function f(S){return t&&S.alternate===null&&(S.flags|=67108866),S}function d(S,y,w,j){return y===null||y.tag!==6?(y=ls(w,S.mode,j),y.return=S,y):(y=o(y,w),y.return=S,y)}function p(S,y,w,j){var F=w.type;return F===jt?Z(S,y,w.props.children,j,w.key):y!==null&&(y.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===xe&&yf(F)===y.type)?(y=o(y,w.props),gs(y,w),y.return=S,y):(y=Ra(w.type,w.key,w.props,null,S.mode,j),gs(y,w),y.return=S,y)}function M(S,y,w,j){return y===null||y.tag!==4||y.stateNode.containerInfo!==w.containerInfo||y.stateNode.implementation!==w.implementation?(y=hl(w,S.mode,j),y.return=S,y):(y=o(y,w.children||[]),y.return=S,y)}function Z(S,y,w,j,F){return y===null||y.tag!==7?(y=Di(w,S.mode,j,F),y.return=S,y):(y=o(y,w),y.return=S,y)}function U(S,y,w){if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return y=ls(""+y,S.mode,w),y.return=S,y;if(typeof y=="object"&&y!==null){switch(y.$$typeof){case ut:return w=Ra(y.type,y.key,y.props,null,S.mode,w),gs(w,y),w.return=S,w;case kt:return y=hl(y,S.mode,w),y.return=S,y;case xe:var j=y._init;return y=j(y._payload),U(S,y,w)}if(ae(y)||Et(y))return y=Di(y,S.mode,w,null),y.return=S,y;if(typeof y.then=="function")return U(S,qo(y),w);if(y.$$typeof===wt)return U(S,zo(S,y),w);ko(S,y)}return null}function z(S,y,w,j){var F=y!==null?y.key:null;if(typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint")return F!==null?null:d(S,y,""+w,j);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case ut:return w.key===F?p(S,y,w,j):null;case kt:return w.key===F?M(S,y,w,j):null;case xe:return F=w._init,w=F(w._payload),z(S,y,w,j)}if(ae(w)||Et(w))return F!==null?null:Z(S,y,w,j,null);if(typeof w.then=="function")return z(S,y,qo(w),j);if(w.$$typeof===wt)return z(S,y,zo(S,w),j);ko(S,w)}return null}function O(S,y,w,j,F){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return S=S.get(w)||null,d(y,S,""+j,F);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case ut:return S=S.get(j.key===null?w:j.key)||null,p(y,S,j,F);case kt:return S=S.get(j.key===null?w:j.key)||null,M(y,S,j,F);case xe:var pt=j._init;return j=pt(j._payload),O(S,y,w,j,F)}if(ae(j)||Et(j))return S=S.get(w)||null,Z(y,S,j,F,null);if(typeof j.then=="function")return O(S,y,w,qo(j),F);if(j.$$typeof===wt)return O(S,y,w,zo(y,j),F);ko(y,j)}return null}function lt(S,y,w,j){for(var F=null,pt=null,it=y,at=y=0,ce=null;it!==null&&at<w.length;at++){it.index>at?(ce=it,it=null):ce=it.sibling;var Tt=z(S,it,w[at],j);if(Tt===null){it===null&&(it=ce);break}t&&it&&Tt.alternate===null&&e(S,it),y=u(Tt,y,at),pt===null?F=Tt:pt.sibling=Tt,pt=Tt,it=ce}if(at===w.length)return n(S,it),st&&T(S,at),F;if(it===null){for(;at<w.length;at++)it=U(S,w[at],j),it!==null&&(y=u(it,y,at),pt===null?F=it:pt.sibling=it,pt=it);return st&&T(S,at),F}for(it=l(it);at<w.length;at++)ce=O(it,S,at,w[at],j),ce!==null&&(t&&ce.alternate!==null&&it.delete(ce.key===null?at:ce.key),y=u(ce,y,at),pt===null?F=ce:pt.sibling=ce,pt=ce);return t&&it.forEach(function(ha){return e(S,ha)}),st&&T(S,at),F}function nt(S,y,w,j){if(w==null)throw Error(v(151));for(var F=null,pt=null,it=y,at=y=0,ce=null,Tt=w.next();it!==null&&!Tt.done;at++,Tt=w.next()){it.index>at?(ce=it,it=null):ce=it.sibling;var ha=z(S,it,Tt.value,j);if(ha===null){it===null&&(it=ce);break}t&&it&&ha.alternate===null&&e(S,it),y=u(ha,y,at),pt===null?F=ha:pt.sibling=ha,pt=ha,it=ce}if(Tt.done)return n(S,it),st&&T(S,at),F;if(it===null){for(;!Tt.done;at++,Tt=w.next())Tt=U(S,Tt.value,j),Tt!==null&&(y=u(Tt,y,at),pt===null?F=Tt:pt.sibling=Tt,pt=Tt);return st&&T(S,at),F}for(it=l(it);!Tt.done;at++,Tt=w.next())Tt=O(it,S,at,Tt.value,j),Tt!==null&&(t&&Tt.alternate!==null&&it.delete(Tt.key===null?at:Tt.key),y=u(Tt,y,at),pt===null?F=Tt:pt.sibling=Tt,pt=Tt);return t&&it.forEach(function(Ym){return e(S,Ym)}),st&&T(S,at),F}function Nt(S,y,w,j){if(typeof w=="object"&&w!==null&&w.type===jt&&w.key===null&&(w=w.props.children),typeof w=="object"&&w!==null){switch(w.$$typeof){case ut:t:{for(var F=w.key;y!==null;){if(y.key===F){if(F=w.type,F===jt){if(y.tag===7){n(S,y.sibling),j=o(y,w.props.children),j.return=S,S=j;break t}}else if(y.elementType===F||typeof F=="object"&&F!==null&&F.$$typeof===xe&&yf(F)===y.type){n(S,y.sibling),j=o(y,w.props),gs(j,w),j.return=S,S=j;break t}n(S,y);break}else e(S,y);y=y.sibling}w.type===jt?(j=Di(w.props.children,S.mode,j,w.key),j.return=S,S=j):(j=Ra(w.type,w.key,w.props,null,S.mode,j),gs(j,w),j.return=S,S=j)}return f(S);case kt:t:{for(F=w.key;y!==null;){if(y.key===F)if(y.tag===4&&y.stateNode.containerInfo===w.containerInfo&&y.stateNode.implementation===w.implementation){n(S,y.sibling),j=o(y,w.children||[]),j.return=S,S=j;break t}else{n(S,y);break}else e(S,y);y=y.sibling}j=hl(w,S.mode,j),j.return=S,S=j}return f(S);case xe:return F=w._init,w=F(w._payload),Nt(S,y,w,j)}if(ae(w))return lt(S,y,w,j);if(Et(w)){if(F=Et(w),typeof F!="function")throw Error(v(150));return w=F.call(w),nt(S,y,w,j)}if(typeof w.then=="function")return Nt(S,y,qo(w),j);if(w.$$typeof===wt)return Nt(S,y,zo(S,w),j);ko(S,w)}return typeof w=="string"&&w!==""||typeof w=="number"||typeof w=="bigint"?(w=""+w,y!==null&&y.tag===6?(n(S,y.sibling),j=o(y,w),j.return=S,S=j):(n(S,y),j=ls(w,S.mode,j),j.return=S,S=j),f(S)):n(S,y)}return function(S,y,w,j){try{vs=0;var F=Nt(S,y,w,j);return bl=null,F}catch(it){if(it===us||it===Ao)throw it;var pt=Ze(29,it,null,S.mode);return pt.lanes=j,pt.return=S,pt}finally{}}}var xl=bf(!0),xf=bf(!1),fi=R(null),Bi=null;function Fn(t){var e=t.alternate;G(ee,ee.current&1),G(fi,t),Bi===null&&(e===null||pl.current!==null||e.memoizedState!==null)&&(Bi=t)}function Sf(t){if(t.tag===22){if(G(ee,ee.current),G(fi,t),Bi===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(Bi=t)}}else In()}function In(){G(ee,ee.current),G(fi,fi.current)}function fn(t){Y(fi),Bi===t&&(Bi=null),Y(ee)}var ee=R(0);function Po(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||nc(n)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function mr(t,e,n,l){e=t.memoizedState,n=n(l,e),n=n==null?e:q({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var _r={enqueueSetState:function(t,e,n){t=t._reactInternals;var l=ei(),o=Kn(l);o.payload=e,n!=null&&(o.callback=n),e=Jn(t,o,l),e!==null&&(ii(e,t,l),cs(e,t,l))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var l=ei(),o=Kn(l);o.tag=1,o.payload=e,n!=null&&(o.callback=n),e=Jn(t,o,l),e!==null&&(ii(e,t,l),cs(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=ei(),l=Kn(n);l.tag=2,e!=null&&(l.callback=e),e=Jn(t,l,n),e!==null&&(ii(e,t,n),cs(e,t,n))}};function Tf(t,e,n,l,o,u,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,f):e.prototype&&e.prototype.isPureReactComponent?!De(n,l)||!De(o,u):!0}function wf(t,e,n,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,l),e.state!==t&&_r.enqueueReplaceState(e,e.state,null)}function ka(t,e){var n=e;if("ref"in e){n={};for(var l in e)l!=="ref"&&(n[l]=e[l])}if(t=t.defaultProps){n===e&&(n=q({},n));for(var o in t)n[o]===void 0&&(n[o]=t[o])}return n}var Go=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Mf(t){Go(t)}function Ef(t){console.error(t)}function zf(t){Go(t)}function Yo(t,e){try{var n=t.onUncaughtError;n(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Lf(t,e,n){try{var l=t.onCaughtError;l(n.value,{componentStack:n.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(o){setTimeout(function(){throw o})}}function pr(t,e,n){return n=Kn(n),n.tag=3,n.payload={element:null},n.callback=function(){Yo(t,e)},n}function Af(t){return t=Kn(t),t.tag=3,t}function Of(t,e,n,l){var o=n.type.getDerivedStateFromError;if(typeof o=="function"){var u=l.value;t.payload=function(){return o(u)},t.callback=function(){Lf(e,n,l)}}var f=n.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Lf(e,n,l),typeof o!="function"&&(aa===null?aa=new Set([this]):aa.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function Yd(t,e,n,l,o){if(n.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=n.alternate,e!==null&&ja(e,n,o,!0),n=fi.current,n!==null){switch(n.tag){case 13:return Bi===null?qr():n.alternate===null&&Kt===0&&(Kt=3),n.flags&=-257,n.flags|=65536,n.lanes=o,l===Vu?n.flags|=16384:(e=n.updateQueue,e===null?n.updateQueue=new Set([l]):e.add(l),Pr(t,l,o)),!1;case 22:return n.flags|=65536,l===Vu?n.flags|=16384:(e=n.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},n.updateQueue=e):(n=e.retryQueue,n===null?e.retryQueue=new Set([l]):n.add(l)),Pr(t,l,o)),!1}throw Error(v(435,n.tag))}return Pr(t,l,o),qr(),!1}if(st)return e=fi.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=o,l!==oe&&(t=Error(v(422),{cause:l}),Xn(Re(t,n)))):(l!==oe&&(e=Error(v(423),{cause:l}),Xn(Re(e,n))),t=t.current.alternate,t.flags|=65536,o&=-o,t.lanes|=o,l=Re(l,n),o=pr(t.stateNode,l,o),Ku(t,o),Kt!==4&&(Kt=2)),!1;var u=Error(v(520),{cause:l});if(u=Re(u,n),Ms===null?Ms=[u]:Ms.push(u),Kt!==4&&(Kt=2),e===null)return!0;l=Re(l,n),n=e;do{switch(n.tag){case 3:return n.flags|=65536,t=o&-o,n.lanes|=t,t=pr(n.stateNode,l,t),Ku(n,t),!1;case 1:if(e=n.type,u=n.stateNode,(n.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(aa===null||!aa.has(u))))return n.flags|=65536,o&=-o,n.lanes|=o,o=Af(o),Of(o,t,n,l),Ku(n,o),!1}n=n.return}while(n!==null);return!1}var Nf=Error(v(461)),ue=!1;function ve(t,e,n,l){e.child=t===null?xf(e,null,n,l):xl(e,t.child,n,l)}function Cf(t,e,n,l,o){n=n.render;var u=e.ref;if("ref"in l){var f={};for(var d in l)d!=="ref"&&(f[d]=l[d])}else f=l;return Ua(e),l=$u(t,e,n,f,u,o),d=tr(),t!==null&&!ue?(er(t,e,o),hn(t,e,o)):(st&&d&&H(e),e.flags|=1,ve(t,e,l,o),e.child)}function Df(t,e,n,l,o){if(t===null){var u=n.type;return typeof u=="function"&&!fl(u)&&u.defaultProps===void 0&&n.compare===null?(e.tag=15,e.type=u,Rf(t,e,u,l,o)):(t=Ra(n.type,null,l,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!wr(t,o)){var f=u.memoizedProps;if(n=n.compare,n=n!==null?n:De,n(f,l)&&t.ref===e.ref)return hn(t,e,o)}return e.flags|=1,t=ri(u,l),t.ref=e.ref,t.return=e,e.child=t}function Rf(t,e,n,l,o){if(t!==null){var u=t.memoizedProps;if(De(u,l)&&t.ref===e.ref)if(ue=!1,e.pendingProps=l=u,wr(t,o))(t.flags&131072)!==0&&(ue=!0);else return e.lanes=t.lanes,hn(t,e,o)}return vr(t,e,n,l,o)}function Bf(t,e,n){var l=e.pendingProps,o=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=u!==null?u.baseLanes|n:n,t!==null){for(o=e.child=t.child,u=0;o!==null;)u=u|o.lanes|o.childLanes,o=o.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Zf(t,e,l,n)}if((n&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&Lo(e,u!==null?u.cachePool:null),u!==null?Dc(e,u):Wu(),Sf(e);else return e.lanes=e.childLanes=536870912,Zf(t,e,u!==null?u.baseLanes|n:n,n)}else u!==null?(Lo(e,u.cachePool),Dc(e,u),In(),e.memoizedState=null):(t!==null&&Lo(e,null),Wu(),In());return ve(t,e,o,n),e.child}function Zf(t,e,n,l){var o=Yu();return o=o===null?null:{parent:te._currentValue,pool:o},e.memoizedState={baseLanes:n,cachePool:o},t!==null&&Lo(e,null),Wu(),Sf(e),t!==null&&ja(t,e,l,!0),null}function Vo(t,e){var n=e.ref;if(n===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof n!="function"&&typeof n!="object")throw Error(v(284));(t===null||t.ref!==n)&&(e.flags|=4194816)}}function vr(t,e,n,l,o){return Ua(e),n=$u(t,e,n,l,void 0,o),l=tr(),t!==null&&!ue?(er(t,e,o),hn(t,e,o)):(st&&l&&H(e),e.flags|=1,ve(t,e,n,o),e.child)}function jf(t,e,n,l,o,u){return Ua(e),e.updateQueue=null,n=Bc(e,l,n,o),Rc(t),l=tr(),t!==null&&!ue?(er(t,e,u),hn(t,e,u)):(st&&l&&H(e),e.flags|=1,ve(t,e,n,u),e.child)}function Uf(t,e,n,l,o){if(Ua(e),e.stateNode===null){var u=un,f=n.contextType;typeof f=="object"&&f!==null&&(u=Ee(f)),u=new n(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=_r,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},Xu(e),f=n.contextType,u.context=typeof f=="object"&&f!==null?Ee(f):un,u.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(mr(e,n,f,l),u.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&_r.enqueueReplaceState(u,u.state,null),hs(e,l,u,o),fs(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var d=e.memoizedProps,p=ka(n,d);u.props=p;var M=u.context,Z=n.contextType;f=un,typeof Z=="object"&&Z!==null&&(f=Ee(Z));var U=n.getDerivedStateFromProps;Z=typeof U=="function"||typeof u.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,Z||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d||M!==f)&&wf(e,u,l,f),Qn=!1;var z=e.memoizedState;u.state=z,hs(e,l,u,o),fs(),M=e.memoizedState,d||z!==M||Qn?(typeof U=="function"&&(mr(e,n,U,l),M=e.memoizedState),(p=Qn||Tf(e,n,p,l,z,M,f))?(Z||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=M),u.props=l,u.state=M,u.context=f,l=p):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,Qu(t,e),f=e.memoizedProps,Z=ka(n,f),u.props=Z,U=e.pendingProps,z=u.context,M=n.contextType,p=un,typeof M=="object"&&M!==null&&(p=Ee(M)),d=n.getDerivedStateFromProps,(M=typeof d=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==U||z!==p)&&wf(e,u,l,p),Qn=!1,z=e.memoizedState,u.state=z,hs(e,l,u,o),fs();var O=e.memoizedState;f!==U||z!==O||Qn||t!==null&&t.dependencies!==null&&Eo(t.dependencies)?(typeof d=="function"&&(mr(e,n,d,l),O=e.memoizedState),(Z=Qn||Tf(e,n,Z,l,z,O,p)||t!==null&&t.dependencies!==null&&Eo(t.dependencies))?(M||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,O,p),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,O,p)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=O),u.props=l,u.state=O,u.context=p,l=Z):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&z===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,Vo(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,n=l&&typeof n.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=xl(e,t.child,null,o),e.child=xl(e,null,n,o)):ve(t,e,n,o),e.memoizedState=u.state,t=e.child):t=hn(t,e,o),t}function Hf(t,e,n,l){return Vn(),e.flags|=256,ve(t,e,n,l),e.child}var gr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function yr(t){return{baseLanes:t,cachePool:Mc()}}function br(t,e,n){return t=t!==null?t.childLanes&~n:0,e&&(t|=hi),t}function qf(t,e,n){var l=e.pendingProps,o=!1,u=(e.flags&128)!==0,f;if((f=u)||(f=t!==null&&t.memoizedState===null?!1:(ee.current&2)!==0),f&&(o=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(st){if(o?Fn(e):In(),st){var d=tt,p;if(p=d){t:{for(p=d,d=Yt;p.nodeType!==8;){if(!d){d=null;break t}if(p=wi(p.nextSibling),p===null){d=null;break t}}d=p}d!==null?(e.memoizedState={dehydrated:d,treeContext:c!==null?{id:h,overflow:_}:null,retryLane:536870912,hydrationErrors:null},p=Ze(18,null,null,0),p.stateNode=d,p.return=e,e.child=p,W=e,tt=null,p=!0):p=!1}p||ke(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return nc(d)?e.lanes=32:e.lanes=536870912,null;fn(e)}return d=l.children,l=l.fallback,o?(In(),o=e.mode,d=Xo({mode:"hidden",children:d},o),l=Di(l,o,n,null),d.return=e,l.return=e,d.sibling=l,e.child=d,o=e.child,o.memoizedState=yr(n),o.childLanes=br(t,f,n),e.memoizedState=gr,l):(Fn(e),xr(e,d))}if(p=t.memoizedState,p!==null&&(d=p.dehydrated,d!==null)){if(u)e.flags&256?(Fn(e),e.flags&=-257,e=Sr(t,e,n)):e.memoizedState!==null?(In(),e.child=t.child,e.flags|=128,e=null):(In(),o=l.fallback,d=e.mode,l=Xo({mode:"visible",children:l.children},d),o=Di(o,d,n,null),o.flags|=2,l.return=e,o.return=e,l.sibling=o,e.child=l,xl(e,t.child,null,n),l=e.child,l.memoizedState=yr(n),l.childLanes=br(t,f,n),e.memoizedState=gr,e=o);else if(Fn(e),nc(d)){if(f=d.nextSibling&&d.nextSibling.dataset,f)var M=f.dgst;f=M,l=Error(v(419)),l.stack="",l.digest=f,Xn({value:l,source:null,stack:null}),e=Sr(t,e,n)}else if(ue||ja(t,e,n,!1),f=(n&t.childLanes)!==0,ue||f){if(f=Zt,f!==null&&(l=n&-n,l=(l&42)!==0?1:Hl(l),l=(l&(f.suspendedLanes|n))!==0?0:l,l!==0&&l!==p.retryLane))throw p.retryLane=l,Gn(t,l),ii(f,t,l),Nf;d.data==="$?"||qr(),e=Sr(t,e,n)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=p.treeContext,tt=wi(d.nextSibling),W=e,st=!0,Bt=null,Yt=!1,t!==null&&(s[r++]=h,s[r++]=_,s[r++]=c,h=t.id,_=t.overflow,c=e),e=xr(e,l.children),e.flags|=4096);return e}return o?(In(),o=l.fallback,d=e.mode,p=t.child,M=p.sibling,l=ri(p,{mode:"hidden",children:l.children}),l.subtreeFlags=p.subtreeFlags&65011712,M!==null?o=ri(M,o):(o=Di(o,d,n,null),o.flags|=2),o.return=e,l.return=e,l.sibling=o,e.child=l,l=o,o=e.child,d=t.child.memoizedState,d===null?d=yr(n):(p=d.cachePool,p!==null?(M=te._currentValue,p=p.parent!==M?{parent:M,pool:M}:p):p=Mc(),d={baseLanes:d.baseLanes|n,cachePool:p}),o.memoizedState=d,o.childLanes=br(t,f,n),e.memoizedState=gr,l):(Fn(e),n=t.child,t=n.sibling,n=ri(n,{mode:"visible",children:l.children}),n.return=e,n.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=n,e.memoizedState=null,n)}function xr(t,e){return e=Xo({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Xo(t,e){return t=Ze(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function Sr(t,e,n){return xl(e,t.child,null,n),t=xr(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function kf(t,e,n){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Za(t.return,e,n)}function Tr(t,e,n,l,o){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:o}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=n,u.tailMode=o)}function Pf(t,e,n){var l=e.pendingProps,o=l.revealOrder,u=l.tail;if(ve(t,e,l.children,n),l=ee.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&kf(t,n,e);else if(t.tag===19)kf(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(G(ee,l),o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Po(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),Tr(e,!1,o,n,u);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Po(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}Tr(e,!0,n,null,u);break;case"together":Tr(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function hn(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),na|=e.lanes,(n&e.childLanes)===0)if(t!==null){if(ja(t,e,n,!1),(n&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(v(153));if(e.child!==null){for(t=e.child,n=ri(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=ri(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function wr(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Eo(t)))}function Vd(t,e,n){switch(e.tag){case 3:xt(e,e.stateNode.containerInfo),Ri(e,te,t.memoizedState.cache),Vn();break;case 27:case 5:ma(e);break;case 4:xt(e,e.stateNode.containerInfo);break;case 10:Ri(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(Fn(e),e.flags|=128,null):(n&e.child.childLanes)!==0?qf(t,e,n):(Fn(e),t=hn(t,e,n),t!==null?t.sibling:null);Fn(e);break;case 19:var o=(t.flags&128)!==0;if(l=(n&e.childLanes)!==0,l||(ja(t,e,n,!1),l=(n&e.childLanes)!==0),o){if(l)return Pf(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),G(ee,ee.current),l)break;return null;case 22:case 23:return e.lanes=0,Bf(t,e,n);case 24:Ri(e,te,t.memoizedState.cache)}return hn(t,e,n)}function Gf(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps)ue=!0;else{if(!wr(t,n)&&(e.flags&128)===0)return ue=!1,Vd(t,e,n);ue=(t.flags&131072)!==0}else ue=!1,st&&(e.flags&1048576)!==0&&N(e,a,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,o=l._init;if(l=o(l._payload),e.type=l,typeof l=="function")fl(l)?(t=ka(l,t),e.tag=1,e=Uf(null,e,l,t,n)):(e.tag=0,e=vr(null,e,l,t,n));else{if(l!=null){if(o=l.$$typeof,o===fe){e.tag=11,e=Cf(null,e,l,t,n);break t}else if(o===be){e.tag=14,e=Df(null,e,l,t,n);break t}}throw e=de(l)||l,Error(v(306,e,""))}}return e;case 0:return vr(t,e,e.type,e.pendingProps,n);case 1:return l=e.type,o=ka(l,e.pendingProps),Uf(t,e,l,o,n);case 3:t:{if(xt(e,e.stateNode.containerInfo),t===null)throw Error(v(387));l=e.pendingProps;var u=e.memoizedState;o=u.element,Qu(t,e),hs(e,l,null,n);var f=e.memoizedState;if(l=f.cache,Ri(e,te,l),l!==u.cache&&dl(e,[te],n,!0),fs(),l=f.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=Hf(t,e,l,n);break t}else if(l!==o){o=Re(Error(v(424)),e),Xn(o),e=Hf(t,e,l,n);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(tt=wi(t.firstChild),W=e,st=!0,Bt=null,Yt=!0,n=xf(e,null,l,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling}else{if(Vn(),l===o){e=hn(t,e,n);break t}ve(t,e,l,n)}e=e.child}return e;case 26:return Vo(t,e),t===null?(n=Qh(e.type,null,e.pendingProps,null))?e.memoizedState=n:st||(n=e.type,t=e.pendingProps,l=su(X.current).createElement(n),l[le]=e,l[Te]=t,ye(l,n,t),Ft(l),e.stateNode=l):e.memoizedState=Qh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return ma(e),t===null&&st&&(l=e.stateNode=Yh(e.type,e.pendingProps,X.current),W=e,Yt=!0,o=tt,oa(e.type)?(ac=o,tt=wi(l.firstChild)):tt=o),ve(t,e,e.pendingProps.children,n),Vo(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&st&&((o=l=tt)&&(l=ym(l,e.type,e.pendingProps,Yt),l!==null?(e.stateNode=l,W=e,tt=wi(l.firstChild),Yt=!1,o=!0):o=!1),o||ke(e)),ma(e),o=e.type,u=e.pendingProps,f=t!==null?t.memoizedProps:null,l=u.children,tc(o,u)?l=null:f!==null&&tc(o,f)&&(e.flags|=32),e.memoizedState!==null&&(o=$u(t,e,jd,null,null,n),Rs._currentValue=o),Vo(t,e),ve(t,e,l,n),e.child;case 6:return t===null&&st&&((t=n=tt)&&(n=bm(n,e.pendingProps,Yt),n!==null?(e.stateNode=n,W=e,tt=null,t=!0):t=!1),t||ke(e)),null;case 13:return qf(t,e,n);case 4:return xt(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=xl(e,null,l,n):ve(t,e,l,n),e.child;case 11:return Cf(t,e,e.type,e.pendingProps,n);case 7:return ve(t,e,e.pendingProps,n),e.child;case 8:return ve(t,e,e.pendingProps.children,n),e.child;case 12:return ve(t,e,e.pendingProps.children,n),e.child;case 10:return l=e.pendingProps,Ri(e,e.type,l.value),ve(t,e,l.children,n),e.child;case 9:return o=e.type._context,l=e.pendingProps.children,Ua(e),o=Ee(o),l=l(o),e.flags|=1,ve(t,e,l,n),e.child;case 14:return Df(t,e,e.type,e.pendingProps,n);case 15:return Rf(t,e,e.type,e.pendingProps,n);case 19:return Pf(t,e,n);case 31:return l=e.pendingProps,n=e.mode,l={mode:l.mode,children:l.children},t===null?(n=Xo(l,n),n.ref=e.ref,e.child=n,n.return=e,e=n):(n=ri(t.child,l),n.ref=e.ref,e.child=n,n.return=e,e=n),e;case 22:return Bf(t,e,n);case 24:return Ua(e),l=Ee(te),t===null?(o=Yu(),o===null&&(o=Zt,u=Pu(),o.pooledCache=u,u.refCount++,u!==null&&(o.pooledCacheLanes|=n),o=u),e.memoizedState={parent:l,cache:o},Xu(e),Ri(e,te,o)):((t.lanes&n)!==0&&(Qu(t,e),hs(e,null,null,n),fs()),o=t.memoizedState,u=e.memoizedState,o.parent!==l?(o={parent:l,cache:l},e.memoizedState=o,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=o),Ri(e,te,l)):(l=u.cache,Ri(e,te,l),l!==o.cache&&dl(e,[te],n,!0))),ve(t,e,e.pendingProps.children,n),e.child;case 29:throw e.pendingProps}throw Error(v(156,e.tag))}function dn(t){t.flags|=4}function Yf(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!Ih(e)){if(e=fi.current,e!==null&&((bt&4194048)===bt?Bi!==null:(bt&62914560)!==bt&&(bt&536870912)===0||e!==Bi))throw rs=Vu,Ec;t.flags|=8192}}function Qo(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Ul():536870912,t.lanes|=e,Ml|=e)}function ys(t,e){if(!st)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Vt(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,l=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags&65011712,l|=o.flags&65011712,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,l|=o.subtreeFlags,l|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=l,t.childLanes=n,e}function Xd(t,e,n){var l=e.pendingProps;switch(K(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Vt(e),null;case 1:return Vt(e),null;case 3:return n=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Si(te),mi(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(t===null||t.child===null)&&(Ba(e)?dn(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Mo())),Vt(e),null;case 26:return n=e.memoizedState,t===null?(dn(e),n!==null?(Vt(e),Yf(e,n)):(Vt(e),e.flags&=-16777217)):n?n!==t.memoizedState?(dn(e),Vt(e),Yf(e,n)):(Vt(e),e.flags&=-16777217):(t.memoizedProps!==l&&dn(e),Vt(e),e.flags&=-16777217),null;case 27:yn(e),n=X.current;var o=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(!l){if(e.stateNode===null)throw Error(v(166));return Vt(e),null}t=J.current,Ba(e)?To(e):(t=Yh(o,l,n),e.stateNode=t,dn(e))}return Vt(e),null;case 5:if(yn(e),n=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(!l){if(e.stateNode===null)throw Error(v(166));return Vt(e),null}if(t=J.current,Ba(e))To(e);else{switch(o=su(X.current),t){case 1:t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":t=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":t=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":t=o.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?o.createElement("select",{is:l.is}):o.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?o.createElement(n,{is:l.is}):o.createElement(n)}}t[le]=e,t[Te]=l;t:for(o=e.child;o!==null;){if(o.tag===5||o.tag===6)t.appendChild(o.stateNode);else if(o.tag!==4&&o.tag!==27&&o.child!==null){o.child.return=o,o=o.child;continue}if(o===e)break t;for(;o.sibling===null;){if(o.return===null||o.return===e)break t;o=o.return}o.sibling.return=o.return,o=o.sibling}e.stateNode=t;t:switch(ye(t,n,l),n){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&dn(e)}}return Vt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&dn(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(v(166));if(t=X.current,Ba(e)){if(t=e.stateNode,n=e.memoizedProps,l=null,o=W,o!==null)switch(o.tag){case 27:case 5:l=o.memoizedProps}t[le]=e,t=!!(t.nodeValue===n||l!==null&&l.suppressHydrationWarning===!0||jh(t.nodeValue,n)),t||ke(e)}else t=su(t).createTextNode(l),t[le]=e,e.stateNode=t}return Vt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(o=Ba(e),l!==null&&l.dehydrated!==null){if(t===null){if(!o)throw Error(v(318));if(o=e.memoizedState,o=o!==null?o.dehydrated:null,!o)throw Error(v(317));o[le]=e}else Vn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Vt(e),o=!1}else o=Mo(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=o),o=!0;if(!o)return e.flags&256?(fn(e),e):(fn(e),null)}if(fn(e),(e.flags&128)!==0)return e.lanes=n,e;if(n=l!==null,t=t!==null&&t.memoizedState!==null,n){l=e.child,o=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(o=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==o&&(l.flags|=2048)}return n!==t&&n&&(e.child.flags|=8192),Qo(e,e.updateQueue),Vt(e),null;case 4:return mi(),t===null&&Jr(e.stateNode.containerInfo),Vt(e),null;case 10:return Si(e.type),Vt(e),null;case 19:if(Y(ee),o=e.memoizedState,o===null)return Vt(e),null;if(l=(e.flags&128)!==0,u=o.rendering,u===null)if(l)ys(o,!1);else{if(Kt!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Po(t),u!==null){for(e.flags|=128,ys(o,!1),t=u.updateQueue,e.updateQueue=t,Qo(e,t),e.subtreeFlags=0,t=n,n=e.child;n!==null;)as(n,t),n=n.sibling;return G(ee,ee.current&1|2),e.child}t=t.sibling}o.tail!==null&&Ue()>Wo&&(e.flags|=128,l=!0,ys(o,!1),e.lanes=4194304)}else{if(!l)if(t=Po(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Qo(e,t),ys(o,!0),o.tail===null&&o.tailMode==="hidden"&&!u.alternate&&!st)return Vt(e),null}else 2*Ue()-o.renderingStartTime>Wo&&n!==536870912&&(e.flags|=128,l=!0,ys(o,!1),e.lanes=4194304);o.isBackwards?(u.sibling=e.child,e.child=u):(t=o.last,t!==null?t.sibling=u:e.child=u,o.last=u)}return o.tail!==null?(e=o.tail,o.rendering=e,o.tail=e.sibling,o.renderingStartTime=Ue(),e.sibling=null,t=ee.current,G(ee,l?t&1|2:t&1),e):(Vt(e),null);case 22:case 23:return fn(e),Fu(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(n&536870912)!==0&&(e.flags&128)===0&&(Vt(e),e.subtreeFlags&6&&(e.flags|=8192)):Vt(e),n=e.updateQueue,n!==null&&Qo(e,n.retryQueue),n=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==n&&(e.flags|=2048),t!==null&&Y(Ha),null;case 24:return n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Si(te),Vt(e),null;case 25:return null;case 30:return null}throw Error(v(156,e.tag))}function Qd(t,e){switch(K(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Si(te),mi(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return yn(e),null;case 13:if(fn(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(v(340));Vn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Y(ee),null;case 4:return mi(),null;case 10:return Si(e.type),null;case 22:case 23:return fn(e),Fu(),t!==null&&Y(Ha),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Si(te),null;case 25:return null;default:return null}}function Vf(t,e){switch(K(e),e.tag){case 3:Si(te),mi();break;case 26:case 27:case 5:yn(e);break;case 4:mi();break;case 13:fn(e);break;case 19:Y(ee);break;case 10:Si(e.type);break;case 22:case 23:fn(e),Fu(),t!==null&&Y(Ha);break;case 24:Si(te)}}function bs(t,e){try{var n=e.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var o=l.next;n=o;do{if((n.tag&t)===t){l=void 0;var u=n.create,f=n.inst;l=u(),f.destroy=l}n=n.next}while(n!==o)}}catch(d){Ct(e,e.return,d)}}function $n(t,e,n){try{var l=e.updateQueue,o=l!==null?l.lastEffect:null;if(o!==null){var u=o.next;l=u;do{if((l.tag&t)===t){var f=l.inst,d=f.destroy;if(d!==void 0){f.destroy=void 0,o=e;var p=n,M=d;try{M()}catch(Z){Ct(o,p,Z)}}}l=l.next}while(l!==u)}}catch(Z){Ct(e,e.return,Z)}}function Xf(t){var e=t.updateQueue;if(e!==null){var n=t.stateNode;try{Cc(e,n)}catch(l){Ct(t,t.return,l)}}}function Qf(t,e,n){n.props=ka(t.type,t.memoizedProps),n.state=t.memoizedState;try{n.componentWillUnmount()}catch(l){Ct(t,e,l)}}function xs(t,e){try{var n=t.ref;if(n!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof n=="function"?t.refCleanup=n(l):n.current=l}}catch(o){Ct(t,e,o)}}function Zi(t,e){var n=t.ref,l=t.refCleanup;if(n!==null)if(typeof l=="function")try{l()}catch(o){Ct(t,e,o)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof n=="function")try{n(null)}catch(o){Ct(t,e,o)}else n.current=null}function Kf(t){var e=t.type,n=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":n.autoFocus&&l.focus();break t;case"img":n.src?l.src=n.src:n.srcSet&&(l.srcset=n.srcSet)}}catch(o){Ct(t,t.return,o)}}function Mr(t,e,n){try{var l=t.stateNode;mm(l,t.type,n,e),l[Te]=e}catch(o){Ct(t,t.return,o)}}function Jf(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&oa(t.type)||t.tag===4}function Er(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Jf(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&oa(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function zr(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n).insertBefore(t,e):(e=n.nodeType===9?n.body:n.nodeName==="HTML"?n.ownerDocument.body:n,e.appendChild(t),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=lu));else if(l!==4&&(l===27&&oa(t.type)&&(n=t.stateNode,e=null),t=t.child,t!==null))for(zr(t,e,n),t=t.sibling;t!==null;)zr(t,e,n),t=t.sibling}function Ko(t,e,n){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(l!==4&&(l===27&&oa(t.type)&&(n=t.stateNode),t=t.child,t!==null))for(Ko(t,e,n),t=t.sibling;t!==null;)Ko(t,e,n),t=t.sibling}function Wf(t){var e=t.stateNode,n=t.memoizedProps;try{for(var l=t.type,o=e.attributes;o.length;)e.removeAttributeNode(o[0]);ye(e,l,n),e[le]=t,e[Te]=n}catch(u){Ct(t,t.return,u)}}var mn=!1,Wt=!1,Lr=!1,Ff=typeof WeakSet=="function"?WeakSet:Set,re=null;function Kd(t,e){if(t=t.containerInfo,Ir=hu,t=La(t),Aa(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else t:{n=(n=t.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var o=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{n.nodeType,u.nodeType}catch{n=null;break t}var f=0,d=-1,p=-1,M=0,Z=0,U=t,z=null;e:for(;;){for(var O;U!==n||o!==0&&U.nodeType!==3||(d=f+o),U!==u||l!==0&&U.nodeType!==3||(p=f+l),U.nodeType===3&&(f+=U.nodeValue.length),(O=U.firstChild)!==null;)z=U,U=O;for(;;){if(U===t)break e;if(z===n&&++M===o&&(d=f),z===u&&++Z===l&&(p=f),(O=U.nextSibling)!==null)break;U=z,z=U.parentNode}U=O}n=d===-1||p===-1?null:{start:d,end:p}}else n=null}n=n||{start:0,end:0}}else n=null;for($r={focusedElem:t,selectionRange:n},hu=!1,re=e;re!==null;)if(e=re,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,re=t;else for(;re!==null;){switch(e=re,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,n=e,o=u.memoizedProps,u=u.memoizedState,l=n.stateNode;try{var lt=ka(n.type,o,n.elementType===n.type);t=l.getSnapshotBeforeUpdate(lt,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(nt){Ct(n,n.return,nt)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,n=t.nodeType,n===9)ic(t);else if(n===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":ic(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(v(163))}if(t=e.sibling,t!==null){t.return=e.return,re=t;break}re=e.return}}function If(t,e,n){var l=n.flags;switch(n.tag){case 0:case 11:case 15:ta(t,n),l&4&&bs(5,n);break;case 1:if(ta(t,n),l&4)if(t=n.stateNode,e===null)try{t.componentDidMount()}catch(f){Ct(n,n.return,f)}else{var o=ka(n.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(o,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){Ct(n,n.return,f)}}l&64&&Xf(n),l&512&&xs(n,n.return);break;case 3:if(ta(t,n),l&64&&(t=n.updateQueue,t!==null)){if(e=null,n.child!==null)switch(n.child.tag){case 27:case 5:e=n.child.stateNode;break;case 1:e=n.child.stateNode}try{Cc(t,e)}catch(f){Ct(n,n.return,f)}}break;case 27:e===null&&l&4&&Wf(n);case 26:case 5:ta(t,n),e===null&&l&4&&Kf(n),l&512&&xs(n,n.return);break;case 12:ta(t,n);break;case 13:ta(t,n),l&4&&eh(t,n),l&64&&(t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(n=nm.bind(null,n),xm(t,n))));break;case 22:if(l=n.memoizedState!==null||mn,!l){e=e!==null&&e.memoizedState!==null||Wt,o=mn;var u=Wt;mn=l,(Wt=e)&&!u?ea(t,n,(n.subtreeFlags&8772)!==0):ta(t,n),mn=o,Wt=u}break;case 30:break;default:ta(t,n)}}function $f(t){var e=t.alternate;e!==null&&(t.alternate=null,$f(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&Ka(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var qt=null,Ge=!1;function _n(t,e,n){for(n=n.child;n!==null;)th(t,e,n),n=n.sibling}function th(t,e,n){if(Se&&typeof Se.onCommitFiberUnmount=="function")try{Se.onCommitFiberUnmount(Pi,n)}catch{}switch(n.tag){case 26:Wt||Zi(n,e),_n(t,e,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode,n.parentNode.removeChild(n));break;case 27:Wt||Zi(n,e);var l=qt,o=Ge;oa(n.type)&&(qt=n.stateNode,Ge=!1),_n(t,e,n),Os(n.stateNode),qt=l,Ge=o;break;case 5:Wt||Zi(n,e);case 6:if(l=qt,o=Ge,qt=null,_n(t,e,n),qt=l,Ge=o,qt!==null)if(Ge)try{(qt.nodeType===9?qt.body:qt.nodeName==="HTML"?qt.ownerDocument.body:qt).removeChild(n.stateNode)}catch(u){Ct(n,e,u)}else try{qt.removeChild(n.stateNode)}catch(u){Ct(n,e,u)}break;case 18:qt!==null&&(Ge?(t=qt,Ph(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,n.stateNode),Us(t)):Ph(qt,n.stateNode));break;case 4:l=qt,o=Ge,qt=n.stateNode.containerInfo,Ge=!0,_n(t,e,n),qt=l,Ge=o;break;case 0:case 11:case 14:case 15:Wt||$n(2,n,e),Wt||$n(4,n,e),_n(t,e,n);break;case 1:Wt||(Zi(n,e),l=n.stateNode,typeof l.componentWillUnmount=="function"&&Qf(n,e,l)),_n(t,e,n);break;case 21:_n(t,e,n);break;case 22:Wt=(l=Wt)||n.memoizedState!==null,_n(t,e,n),Wt=l;break;default:_n(t,e,n)}}function eh(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Us(t)}catch(n){Ct(e,e.return,n)}}function Jd(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Ff),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Ff),e;default:throw Error(v(435,t.tag))}}function Ar(t,e){var n=Jd(t);e.forEach(function(l){var o=am.bind(null,t,l);n.has(l)||(n.add(l),l.then(o,o))})}function Ie(t,e){var n=e.deletions;if(n!==null)for(var l=0;l<n.length;l++){var o=n[l],u=t,f=e,d=f;t:for(;d!==null;){switch(d.tag){case 27:if(oa(d.type)){qt=d.stateNode,Ge=!1;break t}break;case 5:qt=d.stateNode,Ge=!1;break t;case 3:case 4:qt=d.stateNode.containerInfo,Ge=!0;break t}d=d.return}if(qt===null)throw Error(v(160));th(u,f,o),qt=null,Ge=!1,u=o.alternate,u!==null&&(u.return=null),o.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)ih(e,t),e=e.sibling}var Ti=null;function ih(t,e){var n=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ie(e,t),$e(t),l&4&&($n(3,t,t.return),bs(3,t),$n(5,t,t.return));break;case 1:Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),l&64&&mn&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(n=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=n===null?l:n.concat(l))));break;case 26:var o=Ti;if(Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),l&4){var u=n!==null?n.memoizedState:null;if(l=t.memoizedState,n===null)if(l===null)if(t.stateNode===null){t:{l=t.type,n=t.memoizedProps,o=o.ownerDocument||o;e:switch(l){case"title":u=o.getElementsByTagName("title")[0],(!u||u[wn]||u[le]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=o.createElement(l),o.head.insertBefore(u,o.querySelector("head > title"))),ye(u,l,n),u[le]=t,Ft(u),l=u;break t;case"link":var f=Wh("link","href",o).get(l+(n.href||""));if(f){for(var d=0;d<f.length;d++)if(u=f[d],u.getAttribute("href")===(n.href==null||n.href===""?null:n.href)&&u.getAttribute("rel")===(n.rel==null?null:n.rel)&&u.getAttribute("title")===(n.title==null?null:n.title)&&u.getAttribute("crossorigin")===(n.crossOrigin==null?null:n.crossOrigin)){f.splice(d,1);break e}}u=o.createElement(l),ye(u,l,n),o.head.appendChild(u);break;case"meta":if(f=Wh("meta","content",o).get(l+(n.content||""))){for(d=0;d<f.length;d++)if(u=f[d],u.getAttribute("content")===(n.content==null?null:""+n.content)&&u.getAttribute("name")===(n.name==null?null:n.name)&&u.getAttribute("property")===(n.property==null?null:n.property)&&u.getAttribute("http-equiv")===(n.httpEquiv==null?null:n.httpEquiv)&&u.getAttribute("charset")===(n.charSet==null?null:n.charSet)){f.splice(d,1);break e}}u=o.createElement(l),ye(u,l,n),o.head.appendChild(u);break;default:throw Error(v(468,l))}u[le]=t,Ft(u),l=u}t.stateNode=l}else Fh(o,t.type,t.stateNode);else t.stateNode=Jh(o,l,t.memoizedProps);else u!==l?(u===null?n.stateNode!==null&&(n=n.stateNode,n.parentNode.removeChild(n)):u.count--,l===null?Fh(o,t.type,t.stateNode):Jh(o,l,t.memoizedProps)):l===null&&t.stateNode!==null&&Mr(t,t.memoizedProps,n.memoizedProps)}break;case 27:Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),n!==null&&l&4&&Mr(t,t.memoizedProps,n.memoizedProps);break;case 5:if(Ie(e,t),$e(t),l&512&&(Wt||n===null||Zi(n,n.return)),t.flags&32){o=t.stateNode;try{ai(o,"")}catch(O){Ct(t,t.return,O)}}l&4&&t.stateNode!=null&&(o=t.memoizedProps,Mr(t,o,n!==null?n.memoizedProps:o)),l&1024&&(Lr=!0);break;case 6:if(Ie(e,t),$e(t),l&4){if(t.stateNode===null)throw Error(v(162));l=t.memoizedProps,n=t.stateNode;try{n.nodeValue=l}catch(O){Ct(t,t.return,O)}}break;case 3:if(ru=null,o=Ti,Ti=ou(e.containerInfo),Ie(e,t),Ti=o,$e(t),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Us(e.containerInfo)}catch(O){Ct(t,t.return,O)}Lr&&(Lr=!1,nh(t));break;case 4:l=Ti,Ti=ou(t.stateNode.containerInfo),Ie(e,t),$e(t),Ti=l;break;case 12:Ie(e,t),$e(t);break;case 13:Ie(e,t),$e(t),t.child.flags&8192&&t.memoizedState!==null!=(n!==null&&n.memoizedState!==null)&&(Br=Ue()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Ar(t,l)));break;case 22:o=t.memoizedState!==null;var p=n!==null&&n.memoizedState!==null,M=mn,Z=Wt;if(mn=M||o,Wt=Z||p,Ie(e,t),Wt=Z,mn=M,$e(t),l&8192)t:for(e=t.stateNode,e._visibility=o?e._visibility&-2:e._visibility|1,o&&(n===null||p||mn||Wt||Pa(t)),n=null,e=t;;){if(e.tag===5||e.tag===26){if(n===null){p=n=e;try{if(u=p.stateNode,o)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{d=p.stateNode;var U=p.memoizedProps.style,z=U!=null&&U.hasOwnProperty("display")?U.display:null;d.style.display=z==null||typeof z=="boolean"?"":(""+z).trim()}}catch(O){Ct(p,p.return,O)}}}else if(e.tag===6){if(n===null){p=e;try{p.stateNode.nodeValue=o?"":p.memoizedProps}catch(O){Ct(p,p.return,O)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;n===e&&(n=null),e=e.return}n===e&&(n=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(n=l.retryQueue,n!==null&&(l.retryQueue=null,Ar(t,n))));break;case 19:Ie(e,t),$e(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,Ar(t,l)));break;case 30:break;case 21:break;default:Ie(e,t),$e(t)}}function $e(t){var e=t.flags;if(e&2){try{for(var n,l=t.return;l!==null;){if(Jf(l)){n=l;break}l=l.return}if(n==null)throw Error(v(160));switch(n.tag){case 27:var o=n.stateNode,u=Er(t);Ko(t,u,o);break;case 5:var f=n.stateNode;n.flags&32&&(ai(f,""),n.flags&=-33);var d=Er(t);Ko(t,d,f);break;case 3:case 4:var p=n.stateNode.containerInfo,M=Er(t);zr(t,M,p);break;default:throw Error(v(161))}}catch(Z){Ct(t,t.return,Z)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function nh(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;nh(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function ta(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)If(t,e.alternate,e),e=e.sibling}function Pa(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:$n(4,e,e.return),Pa(e);break;case 1:Zi(e,e.return);var n=e.stateNode;typeof n.componentWillUnmount=="function"&&Qf(e,e.return,n),Pa(e);break;case 27:Os(e.stateNode);case 26:case 5:Zi(e,e.return),Pa(e);break;case 22:e.memoizedState===null&&Pa(e);break;case 30:Pa(e);break;default:Pa(e)}t=t.sibling}}function ea(t,e,n){for(n=n&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,o=t,u=e,f=u.flags;switch(u.tag){case 0:case 11:case 15:ea(o,u,n),bs(4,u);break;case 1:if(ea(o,u,n),l=u,o=l.stateNode,typeof o.componentDidMount=="function")try{o.componentDidMount()}catch(M){Ct(l,l.return,M)}if(l=u,o=l.updateQueue,o!==null){var d=l.stateNode;try{var p=o.shared.hiddenCallbacks;if(p!==null)for(o.shared.hiddenCallbacks=null,o=0;o<p.length;o++)Nc(p[o],d)}catch(M){Ct(l,l.return,M)}}n&&f&64&&Xf(u),xs(u,u.return);break;case 27:Wf(u);case 26:case 5:ea(o,u,n),n&&l===null&&f&4&&Kf(u),xs(u,u.return);break;case 12:ea(o,u,n);break;case 13:ea(o,u,n),n&&f&4&&eh(o,u);break;case 22:u.memoizedState===null&&ea(o,u,n),xs(u,u.return);break;case 30:break;default:ea(o,u,n)}e=e.sibling}}function Or(t,e){var n=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==n&&(t!=null&&t.refCount++,n!=null&&ss(n))}function Nr(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ss(t))}function ji(t,e,n,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)ah(t,e,n,l),e=e.sibling}function ah(t,e,n,l){var o=e.flags;switch(e.tag){case 0:case 11:case 15:ji(t,e,n,l),o&2048&&bs(9,e);break;case 1:ji(t,e,n,l);break;case 3:ji(t,e,n,l),o&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&ss(t)));break;case 12:if(o&2048){ji(t,e,n,l),t=e.stateNode;try{var u=e.memoizedProps,f=u.id,d=u.onPostCommit;typeof d=="function"&&d(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(p){Ct(e,e.return,p)}}else ji(t,e,n,l);break;case 13:ji(t,e,n,l);break;case 23:break;case 22:u=e.stateNode,f=e.alternate,e.memoizedState!==null?u._visibility&2?ji(t,e,n,l):Ss(t,e):u._visibility&2?ji(t,e,n,l):(u._visibility|=2,Sl(t,e,n,l,(e.subtreeFlags&10256)!==0)),o&2048&&Or(f,e);break;case 24:ji(t,e,n,l),o&2048&&Nr(e.alternate,e);break;default:ji(t,e,n,l)}}function Sl(t,e,n,l,o){for(o=o&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,f=e,d=n,p=l,M=f.flags;switch(f.tag){case 0:case 11:case 15:Sl(u,f,d,p,o),bs(8,f);break;case 23:break;case 22:var Z=f.stateNode;f.memoizedState!==null?Z._visibility&2?Sl(u,f,d,p,o):Ss(u,f):(Z._visibility|=2,Sl(u,f,d,p,o)),o&&M&2048&&Or(f.alternate,f);break;case 24:Sl(u,f,d,p,o),o&&M&2048&&Nr(f.alternate,f);break;default:Sl(u,f,d,p,o)}e=e.sibling}}function Ss(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var n=t,l=e,o=l.flags;switch(l.tag){case 22:Ss(n,l),o&2048&&Or(l.alternate,l);break;case 24:Ss(n,l),o&2048&&Nr(l.alternate,l);break;default:Ss(n,l)}e=e.sibling}}var Ts=8192;function Tl(t){if(t.subtreeFlags&Ts)for(t=t.child;t!==null;)lh(t),t=t.sibling}function lh(t){switch(t.tag){case 26:Tl(t),t.flags&Ts&&t.memoizedState!==null&&Rm(Ti,t.memoizedState,t.memoizedProps);break;case 5:Tl(t);break;case 3:case 4:var e=Ti;Ti=ou(t.stateNode.containerInfo),Tl(t),Ti=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=Ts,Ts=16777216,Tl(t),Ts=e):Tl(t));break;default:Tl(t)}}function sh(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ws(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];re=l,uh(l,t)}sh(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)oh(t),t=t.sibling}function oh(t){switch(t.tag){case 0:case 11:case 15:ws(t),t.flags&2048&&$n(9,t,t.return);break;case 3:ws(t);break;case 12:ws(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Jo(t)):ws(t);break;default:ws(t)}}function Jo(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var n=0;n<e.length;n++){var l=e[n];re=l,uh(l,t)}sh(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:$n(8,e,e.return),Jo(e);break;case 22:n=e.stateNode,n._visibility&2&&(n._visibility&=-3,Jo(e));break;default:Jo(e)}t=t.sibling}}function uh(t,e){for(;re!==null;){var n=re;switch(n.tag){case 0:case 11:case 15:$n(8,n,e);break;case 23:case 22:if(n.memoizedState!==null&&n.memoizedState.cachePool!==null){var l=n.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ss(n.memoizedState.cache)}if(l=n.child,l!==null)l.return=n,re=l;else t:for(n=t;re!==null;){l=re;var o=l.sibling,u=l.return;if($f(l),l===n){re=null;break t}if(o!==null){o.return=u,re=o;break t}re=u}}}var Wd={getCacheForType:function(t){var e=Ee(te),n=e.data.get(t);return n===void 0&&(n=t(),e.data.set(t,n)),n}},Fd=typeof WeakMap=="function"?WeakMap:Map,zt=0,Zt=null,vt=null,bt=0,Lt=0,ti=null,ia=!1,wl=!1,Cr=!1,pn=0,Kt=0,na=0,Ga=0,Dr=0,hi=0,Ml=0,Ms=null,Ye=null,Rr=!1,Br=0,Wo=1/0,Fo=null,aa=null,ge=0,la=null,El=null,zl=0,Zr=0,jr=null,rh=null,Es=0,Ur=null;function ei(){if((zt&2)!==0&&bt!==0)return bt&-bt;if(D.T!==null){var t=ml;return t!==0?t:Vr()}return Fs()}function ch(){hi===0&&(hi=(bt&536870912)===0||st?Ks():536870912);var t=fi.current;return t!==null&&(t.flags|=32),hi}function ii(t,e,n){(t===Zt&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)&&(Ll(t,0),sa(t,bt,hi,!1)),Sn(t,n),((zt&2)===0||t!==Zt)&&(t===Zt&&((zt&2)===0&&(Ga|=n),Kt===4&&sa(t,bt,hi,!1)),Ui(t))}function fh(t,e,n){if((zt&6)!==0)throw Error(v(327));var l=!n&&(e&124)===0&&(e&t.expiredLanes)===0||pi(t,e),o=l?tm(t,e):kr(t,e,!0),u=l;do{if(o===0){wl&&!l&&sa(t,e,0,!1);break}else{if(n=t.current.alternate,u&&!Id(n)){o=kr(t,e,!1),u=!1;continue}if(o===2){if(u=e,t.errorRecoveryDisabledLanes&u)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var d=t;o=Ms;var p=d.current.memoizedState.isDehydrated;if(p&&(Ll(d,f).flags|=256),f=kr(d,f,!1),f!==2){if(Cr&&!p){d.errorRecoveryDisabledLanes|=u,Ga|=u,o=4;break t}u=Ye,Ye=o,u!==null&&(Ye===null?Ye=u:Ye.push.apply(Ye,u))}o=f}if(u=!1,o!==2)continue}}if(o===1){Ll(t,0),sa(t,e,0,!0);break}t:{switch(l=t,u=o,u){case 0:case 1:throw Error(v(345));case 4:if((e&4194048)!==e)break;case 6:sa(l,e,hi,!ia);break t;case 2:Ye=null;break;case 3:case 5:break;default:throw Error(v(329))}if((e&62914560)===e&&(o=Br+300-Ue(),10<o)){if(sa(l,e,hi,!ia),Xa(l,0,!0)!==0)break t;l.timeoutHandle=qh(hh.bind(null,l,n,Ye,Fo,Rr,e,hi,Ga,Ml,ia,u,2,-0,0),o);break t}hh(l,n,Ye,Fo,Rr,e,hi,Ga,Ml,ia,u,0,-0,0)}}break}while(!0);Ui(t)}function hh(t,e,n,l,o,u,f,d,p,M,Z,U,z,O){if(t.timeoutHandle=-1,U=e.subtreeFlags,(U&8192||(U&16785408)===16785408)&&(Ds={stylesheets:null,count:0,unsuspend:Dm},lh(e),U=Bm(),U!==null)){t.cancelPendingCommit=U(yh.bind(null,t,e,u,n,l,o,f,d,p,Z,1,z,O)),sa(t,u,f,!M);return}yh(t,e,u,n,l,o,f,d,p)}function Id(t){for(var e=t;;){var n=e.tag;if((n===0||n===11||n===15)&&e.flags&16384&&(n=e.updateQueue,n!==null&&(n=n.stores,n!==null)))for(var l=0;l<n.length;l++){var o=n[l],u=o.getSnapshot;o=o.value;try{if(!Ce(u(),o))return!1}catch{return!1}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function sa(t,e,n,l){e&=~Dr,e&=~Ga,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var o=e;0<o;){var u=31-Oe(o),f=1<<u;l[u]=-1,o&=~f}n!==0&&Js(t,n,e)}function Io(){return(zt&6)===0?(zs(0),!1):!0}function Hr(){if(vt!==null){if(Lt===0)var t=vt.return;else t=vt,xi=ci=null,ir(t),bl=null,vs=0,t=vt;for(;t!==null;)Vf(t.alternate,t),t=t.return;vt=null}}function Ll(t,e){var n=t.timeoutHandle;n!==-1&&(t.timeoutHandle=-1,pm(n)),n=t.cancelPendingCommit,n!==null&&(t.cancelPendingCommit=null,n()),Hr(),Zt=t,vt=n=ri(t.current,null),bt=e,Lt=0,ti=null,ia=!1,wl=pi(t,e),Cr=!1,Ml=hi=Dr=Ga=na=Kt=0,Ye=Ms=null,Rr=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var o=31-Oe(l),u=1<<o;e|=t[o],l&=~u}return pn=e,Na(),n}function dh(t,e){dt=null,D.H=Ho,e===us||e===Ao?(e=Ac(),Lt=3):e===Ec?(e=Ac(),Lt=4):Lt=e===Nf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ti=e,vt===null&&(Kt=1,Yo(t,Re(e,t.current)))}function mh(){var t=D.H;return D.H=Ho,t===null?Ho:t}function _h(){var t=D.A;return D.A=Wd,t}function qr(){Kt=4,ia||(bt&4194048)!==bt&&fi.current!==null||(wl=!0),(na&134217727)===0&&(Ga&134217727)===0||Zt===null||sa(Zt,bt,hi,!1)}function kr(t,e,n){var l=zt;zt|=2;var o=mh(),u=_h();(Zt!==t||bt!==e)&&(Fo=null,Ll(t,e)),e=!1;var f=Kt;t:do try{if(Lt!==0&&vt!==null){var d=vt,p=ti;switch(Lt){case 8:Hr(),f=6;break t;case 3:case 2:case 9:case 6:fi.current===null&&(e=!0);var M=Lt;if(Lt=0,ti=null,Al(t,d,p,M),n&&wl){f=0;break t}break;default:M=Lt,Lt=0,ti=null,Al(t,d,p,M)}}$d(),f=Kt;break}catch(Z){dh(t,Z)}while(!0);return e&&t.shellSuspendCounter++,xi=ci=null,zt=l,D.H=o,D.A=u,vt===null&&(Zt=null,bt=0,Na()),f}function $d(){for(;vt!==null;)ph(vt)}function tm(t,e){var n=zt;zt|=2;var l=mh(),o=_h();Zt!==t||bt!==e?(Fo=null,Wo=Ue()+500,Ll(t,e)):wl=pi(t,e);t:do try{if(Lt!==0&&vt!==null){e=vt;var u=ti;e:switch(Lt){case 1:Lt=0,ti=null,Al(t,e,u,1);break;case 2:case 9:if(zc(u)){Lt=0,ti=null,vh(e);break}e=function(){Lt!==2&&Lt!==9||Zt!==t||(Lt=7),Ui(t)},u.then(e,e);break t;case 3:Lt=7;break t;case 4:Lt=5;break t;case 7:zc(u)?(Lt=0,ti=null,vh(e)):(Lt=0,ti=null,Al(t,e,u,7));break;case 5:var f=null;switch(vt.tag){case 26:f=vt.memoizedState;case 5:case 27:var d=vt;if(!f||Ih(f)){Lt=0,ti=null;var p=d.sibling;if(p!==null)vt=p;else{var M=d.return;M!==null?(vt=M,$o(M)):vt=null}break e}}Lt=0,ti=null,Al(t,e,u,5);break;case 6:Lt=0,ti=null,Al(t,e,u,6);break;case 8:Hr(),Kt=6;break t;default:throw Error(v(462))}}em();break}catch(Z){dh(t,Z)}while(!0);return xi=ci=null,D.H=l,D.A=o,zt=n,vt!==null?0:(Zt=null,bt=0,Na(),Kt)}function em(){for(;vt!==null&&!Ps();)ph(vt)}function ph(t){var e=Gf(t.alternate,t,pn);t.memoizedProps=t.pendingProps,e===null?$o(t):vt=e}function vh(t){var e=t,n=e.alternate;switch(e.tag){case 15:case 0:e=jf(n,e,e.pendingProps,e.type,void 0,bt);break;case 11:e=jf(n,e,e.pendingProps,e.type.render,e.ref,bt);break;case 5:ir(e);default:Vf(n,e),e=vt=as(e,pn),e=Gf(n,e,pn)}t.memoizedProps=t.pendingProps,e===null?$o(t):vt=e}function Al(t,e,n,l){xi=ci=null,ir(e),bl=null,vs=0;var o=e.return;try{if(Yd(t,o,e,n,bt)){Kt=1,Yo(t,Re(n,t.current)),vt=null;return}}catch(u){if(o!==null)throw vt=o,u;Kt=1,Yo(t,Re(n,t.current)),vt=null;return}e.flags&32768?(st||l===1?t=!0:wl||(bt&536870912)!==0?t=!1:(ia=t=!0,(l===2||l===9||l===3||l===6)&&(l=fi.current,l!==null&&l.tag===13&&(l.flags|=16384))),gh(e,t)):$o(e)}function $o(t){var e=t;do{if((e.flags&32768)!==0){gh(e,ia);return}t=e.return;var n=Xd(e.alternate,e,pn);if(n!==null){vt=n;return}if(e=e.sibling,e!==null){vt=e;return}vt=e=t}while(e!==null);Kt===0&&(Kt=5)}function gh(t,e){do{var n=Qd(t.alternate,t);if(n!==null){n.flags&=32767,vt=n;return}if(n=t.return,n!==null&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!e&&(t=t.sibling,t!==null)){vt=t;return}vt=t=n}while(t!==null);Kt=6,vt=null}function yh(t,e,n,l,o,u,f,d,p){t.cancelPendingCommit=null;do tu();while(ge!==0);if((zt&6)!==0)throw Error(v(327));if(e!==null){if(e===t.current)throw Error(v(177));if(u=e.lanes|e.childLanes,u|=Fe,Tu(t,n,u,f,d,p),t===Zt&&(vt=Zt=null,bt=0),El=e,la=t,zl=n,Zr=u,jr=o,rh=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,lm(bn,function(){return wh(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=D.T,D.T=null,o=Q.p,Q.p=2,f=zt,zt|=4;try{Kd(t,e,n)}finally{zt=f,Q.p=o,D.T=l}}ge=1,bh(),xh(),Sh()}}function bh(){if(ge===1){ge=0;var t=la,e=El,n=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=zt;zt|=4;try{ih(e,t);var u=$r,f=La(t.containerInfo),d=u.focusedElem,p=u.selectionRange;if(f!==d&&d&&d.ownerDocument&&sl(d.ownerDocument.documentElement,d)){if(p!==null&&Aa(d)){var M=p.start,Z=p.end;if(Z===void 0&&(Z=M),"selectionStart"in d)d.selectionStart=M,d.selectionEnd=Math.min(Z,d.value.length);else{var U=d.ownerDocument||document,z=U&&U.defaultView||window;if(z.getSelection){var O=z.getSelection(),lt=d.textContent.length,nt=Math.min(p.start,lt),Nt=p.end===void 0?nt:Math.min(p.end,lt);!O.extend&&nt>Nt&&(f=Nt,Nt=nt,nt=f);var S=is(d,nt),y=is(d,Nt);if(S&&y&&(O.rangeCount!==1||O.anchorNode!==S.node||O.anchorOffset!==S.offset||O.focusNode!==y.node||O.focusOffset!==y.offset)){var w=U.createRange();w.setStart(S.node,S.offset),O.removeAllRanges(),nt>Nt?(O.addRange(w),O.extend(y.node,y.offset)):(w.setEnd(y.node,y.offset),O.addRange(w))}}}}for(U=[],O=d;O=O.parentNode;)O.nodeType===1&&U.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<U.length;d++){var j=U[d];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}hu=!!Ir,$r=Ir=null}finally{zt=o,Q.p=l,D.T=n}}t.current=e,ge=2}}function xh(){if(ge===2){ge=0;var t=la,e=El,n=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||n){n=D.T,D.T=null;var l=Q.p;Q.p=2;var o=zt;zt|=4;try{If(t,e.alternate,e)}finally{zt=o,Q.p=l,D.T=n}}ge=3}}function Sh(){if(ge===4||ge===3){ge=0,Gs();var t=la,e=El,n=zl,l=rh;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?ge=5:(ge=0,El=la=null,Th(t,t.pendingLanes));var o=t.pendingLanes;if(o===0&&(aa=null),ql(n),e=e.stateNode,Se&&typeof Se.onCommitFiberRoot=="function")try{Se.onCommitFiberRoot(Pi,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=D.T,o=Q.p,Q.p=2,D.T=null;try{for(var u=t.onRecoverableError,f=0;f<l.length;f++){var d=l[f];u(d.value,{componentStack:d.stack})}}finally{D.T=e,Q.p=o}}(zl&3)!==0&&tu(),Ui(t),o=t.pendingLanes,(n&4194090)!==0&&(o&42)!==0?t===Ur?Es++:(Es=0,Ur=t):Es=0,zs(0)}}function Th(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,ss(e)))}function tu(t){return bh(),xh(),Sh(),wh()}function wh(){if(ge!==5)return!1;var t=la,e=Zr;Zr=0;var n=ql(zl),l=D.T,o=Q.p;try{Q.p=32>n?32:n,D.T=null,n=jr,jr=null;var u=la,f=zl;if(ge=0,El=la=null,zl=0,(zt&6)!==0)throw Error(v(331));var d=zt;if(zt|=4,oh(u.current),ah(u,u.current,f,n),zt=d,zs(0,!1),Se&&typeof Se.onPostCommitFiberRoot=="function")try{Se.onPostCommitFiberRoot(Pi,u)}catch{}return!0}finally{Q.p=o,D.T=l,Th(t,e)}}function Mh(t,e,n){e=Re(n,e),e=pr(t.stateNode,e,2),t=Jn(t,e,2),t!==null&&(Sn(t,2),Ui(t))}function Ct(t,e,n){if(t.tag===3)Mh(t,t,n);else for(;e!==null;){if(e.tag===3){Mh(e,t,n);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(aa===null||!aa.has(l))){t=Re(n,t),n=Af(2),l=Jn(e,n,2),l!==null&&(Of(n,l,e,t),Sn(l,2),Ui(l));break}}e=e.return}}function Pr(t,e,n){var l=t.pingCache;if(l===null){l=t.pingCache=new Fd;var o=new Set;l.set(e,o)}else o=l.get(e),o===void 0&&(o=new Set,l.set(e,o));o.has(n)||(Cr=!0,o.add(n),t=im.bind(null,t,e,n),e.then(t,t))}function im(t,e,n){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&n,t.warmLanes&=~n,Zt===t&&(bt&n)===n&&(Kt===4||Kt===3&&(bt&62914560)===bt&&300>Ue()-Br?(zt&2)===0&&Ll(t,0):Dr|=n,Ml===bt&&(Ml=0)),Ui(t)}function Eh(t,e){e===0&&(e=Ul()),t=Gn(t,e),t!==null&&(Sn(t,e),Ui(t))}function nm(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Eh(t,n)}function am(t,e){var n=0;switch(t.tag){case 13:var l=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(v(314))}l!==null&&l.delete(e),Eh(t,n)}function lm(t,e){return _a(t,e)}var eu=null,Ol=null,Gr=!1,iu=!1,Yr=!1,Ya=0;function Ui(t){t!==Ol&&t.next===null&&(Ol===null?eu=Ol=t:Ol=Ol.next=t),iu=!0,Gr||(Gr=!0,om())}function zs(t,e){if(!Yr&&iu){Yr=!0;do for(var n=!1,l=eu;l!==null;){if(t!==0){var o=l.pendingLanes;if(o===0)var u=0;else{var f=l.suspendedLanes,d=l.pingedLanes;u=(1<<31-Oe(42|t)+1)-1,u&=o&~(f&~d),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(n=!0,Oh(l,u))}else u=bt,u=Xa(l,l===Zt?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||pi(l,u)||(n=!0,Oh(l,u));l=l.next}while(n);Yr=!1}}function sm(){zh()}function zh(){iu=Gr=!1;var t=0;Ya!==0&&(_m()&&(t=Ya),Ya=0);for(var e=Ue(),n=null,l=eu;l!==null;){var o=l.next,u=Lh(l,e);u===0?(l.next=null,n===null?eu=o:n.next=o,o===null&&(Ol=n)):(n=l,(t!==0||(u&3)!==0)&&(iu=!0)),l=o}zs(t)}function Lh(t,e){for(var n=t.suspendedLanes,l=t.pingedLanes,o=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var f=31-Oe(u),d=1<<f,p=o[f];p===-1?((d&n)===0||(d&l)!==0)&&(o[f]=Su(d,e)):p<=e&&(t.expiredLanes|=d),u&=~d}if(e=Zt,n=bt,n=Xa(t,t===e?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,n===0||t===e&&(Lt===2||Lt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&Bl(l),t.callbackNode=null,t.callbackPriority=0;if((n&3)===0||pi(t,n)){if(e=n&-n,e===t.callbackPriority)return e;switch(l!==null&&Bl(l),ql(n)){case 2:case 8:n=Zl;break;case 32:n=bn;break;case 268435456:n=jl;break;default:n=bn}return l=Ah.bind(null,t),n=_a(n,l),t.callbackPriority=e,t.callbackNode=n,e}return l!==null&&l!==null&&Bl(l),t.callbackPriority=2,t.callbackNode=null,2}function Ah(t,e){if(ge!==0&&ge!==5)return t.callbackNode=null,t.callbackPriority=0,null;var n=t.callbackNode;if(tu()&&t.callbackNode!==n)return null;var l=bt;return l=Xa(t,t===Zt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(fh(t,l,e),Lh(t,Ue()),t.callbackNode!=null&&t.callbackNode===n?Ah.bind(null,t):null)}function Oh(t,e){if(tu())return null;fh(t,e,!0)}function om(){vm(function(){(zt&6)!==0?_a(Ys,sm):zh()})}function Vr(){return Ya===0&&(Ya=Ks()),Ya}function Nh(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:On(""+t)}function Ch(t,e){var n=e.ownerDocument.createElement("input");return n.name=e.name,n.value=e.value,t.id&&n.setAttribute("form",t.id),e.parentNode.insertBefore(n,e),t=new FormData(t),n.parentNode.removeChild(n),t}function um(t,e,n,l,o){if(e==="submit"&&n&&n.stateNode===o){var u=Nh((o[Te]||null).action),f=l.submitter;f&&(e=(e=f[Te]||null)?Nh(e.formAction):f.getAttribute("formAction"),e!==null&&(u=e,f=null));var d=new Ta("action","action",null,l,o);t.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Ya!==0){var p=f?Ch(o,f):new FormData(o);fr(n,{pending:!0,data:p,method:o.method,action:u},null,p)}}else typeof u=="function"&&(d.preventDefault(),p=f?Ch(o,f):new FormData(o),fr(n,{pending:!0,data:p,method:o.method,action:u},u,p))},currentTarget:o}]})}}for(var Xr=0;Xr<ln.length;Xr++){var Qr=ln[Xr],rm=Qr.toLowerCase(),cm=Qr[0].toUpperCase()+Qr.slice(1);We(rm,"on"+cm)}We(bo,"onAnimationEnd"),We(Je,"onAnimationIteration"),We(Oa,"onAnimationStart"),We("dblclick","onDoubleClick"),We("focusin","onFocus"),We("focusout","onBlur"),We(qu,"onTransitionRun"),We(cl,"onTransitionStart"),We(ku,"onTransitionCancel"),We(ns,"onTransitionEnd"),Xi("onMouseEnter",["mouseout","mouseover"]),Xi("onMouseLeave",["mouseout","mouseover"]),Xi("onPointerEnter",["pointerout","pointerover"]),Xi("onPointerLeave",["pointerout","pointerover"]),Vi("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Vi("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Vi("onBeforeInput",["compositionend","keypress","textInput","paste"]),Vi("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Vi("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Vi("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ls="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),fm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ls));function Dh(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var l=t[n],o=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var f=l.length-1;0<=f;f--){var d=l[f],p=d.instance,M=d.currentTarget;if(d=d.listener,p!==u&&o.isPropagationStopped())break t;u=d,o.currentTarget=M;try{u(o)}catch(Z){Go(Z)}o.currentTarget=null,u=p}else for(f=0;f<l.length;f++){if(d=l[f],p=d.instance,M=d.currentTarget,d=d.listener,p!==u&&o.isPropagationStopped())break t;u=d,o.currentTarget=M;try{u(o)}catch(Z){Go(Z)}o.currentTarget=null,u=p}}}}function gt(t,e){var n=e[He];n===void 0&&(n=e[He]=new Set);var l=t+"__bubble";n.has(l)||(Rh(e,t,2,!1),n.add(l))}function Kr(t,e,n){var l=0;e&&(l|=4),Rh(n,t,l,e)}var nu="_reactListening"+Math.random().toString(36).slice(2);function Jr(t){if(!t[nu]){t[nu]=!0,$s.forEach(function(n){n!=="selectionchange"&&(fm.has(n)||Kr(n,!1,t),Kr(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[nu]||(e[nu]=!0,Kr("selectionchange",!1,e))}}function Rh(t,e,n,l){switch(ad(e)){case 2:var o=Um;break;case 8:o=Hm;break;default:o=rc}n=o.bind(null,e,n,t),o=void 0,!Sa||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),l?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Wr(t,e,n,l,o){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var d=l.stateNode.containerInfo;if(d===o)break;if(f===4)for(f=l.return;f!==null;){var p=f.tag;if((p===3||p===4)&&f.stateNode.containerInfo===o)return;f=f.return}for(;d!==null;){if(f=zi(d),f===null)return;if(p=f.tag,p===5||p===6||p===26||p===27){l=u=f;continue t}d=d.parentNode}}l=l.return}Ve(function(){var M=u,Z=Nn(n),U=[];t:{var z=xo.get(t);if(z!==void 0){var O=Ta,lt=t;switch(t){case"keypress":if(Qt(n)===0)break t;case"keydown":case"keyup":O=Cu;break;case"focusin":lt="focus",O=Ql;break;case"focusout":lt="blur",O=Ql;break;case"beforeblur":case"afterblur":O=Ql;break;case"click":if(n.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=Rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=zu;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=Ru;break;case bo:case Je:case Oa:O=Lu;break;case ns:O=Bu;break;case"scroll":case"scrollend":O=Eu;break;case"wheel":O=co;break;case"copy":case"cut":case"paste":O=Kl;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=Wl;break;case"toggle":case"beforetoggle":O=tn}var nt=(e&4)!==0,Nt=!nt&&(t==="scroll"||t==="scrollend"),S=nt?z!==null?z+"Capture":null:z;nt=[];for(var y=M,w;y!==null;){var j=y;if(w=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||w===null||S===null||(j=St(y,S),j!=null&&nt.push(As(y,j,w))),Nt)break;y=y.return}0<nt.length&&(z=new O(z,lt,null,n,Z),U.push({event:z,listeners:nt}))}}if((e&7)===0){t:{if(z=t==="mouseover"||t==="pointerover",O=t==="mouseout"||t==="pointerout",z&&n!==xa&&(lt=n.relatedTarget||n.fromElement)&&(zi(lt)||lt[Tn]))break t;if((O||z)&&(z=Z.window===Z?Z:(z=Z.ownerDocument)?z.defaultView||z.parentWindow:window,O?(lt=n.relatedTarget||n.toElement,O=M,lt=lt?zi(lt):null,lt!==null&&(Nt=V(lt),nt=lt.tag,lt!==Nt||nt!==5&&nt!==27&&nt!==6)&&(lt=null)):(O=null,lt=M),O!==lt)){if(nt=Rn,j="onMouseLeave",S="onMouseEnter",y="mouse",(t==="pointerout"||t==="pointerover")&&(nt=Wl,j="onPointerLeave",S="onPointerEnter",y="pointer"),Nt=O==null?z:ni(O),w=lt==null?z:ni(lt),z=new nt(j,y+"leave",O,n,Z),z.target=Nt,z.relatedTarget=w,j=null,zi(Z)===M&&(nt=new nt(S,y+"enter",lt,n,Z),nt.target=w,nt.relatedTarget=Nt,j=nt),Nt=j,O&&lt)e:{for(nt=O,S=lt,y=0,w=nt;w;w=Nl(w))y++;for(w=0,j=S;j;j=Nl(j))w++;for(;0<y-w;)nt=Nl(nt),y--;for(;0<w-y;)S=Nl(S),w--;for(;y--;){if(nt===S||S!==null&&nt===S.alternate)break e;nt=Nl(nt),S=Nl(S)}nt=null}else nt=null;O!==null&&Bh(U,z,O,nt,!1),lt!==null&&Nt!==null&&Bh(U,Nt,lt,nt,!0)}}t:{if(z=M?ni(M):window,O=z.nodeName&&z.nodeName.toLowerCase(),O==="select"||O==="input"&&z.type==="file")var F=jn;else if(nn(z))if($l)F=Hu;else{F=Uu;var pt=es}else O=z.nodeName,!O||O.toLowerCase()!=="input"||z.type!=="checkbox"&&z.type!=="radio"?M&&ba(M.elementType)&&(F=jn):F=bi;if(F&&(F=F(t,M))){po(U,F,n,Z);break t}pt&&pt(t,z,M),t==="focusout"&&M&&z.type==="number"&&M.memoizedProps.value!=null&&Ln(z,"number",z.value)}switch(pt=M?ni(M):window,t){case"focusin":(nn(pt)||pt.contentEditable==="true")&&(Ke=pt,qn=M,an=null);break;case"focusout":an=qn=Ke=null;break;case"mousedown":ul=!0;break;case"contextmenu":case"mouseup":case"dragend":ul=!1,go(U,n,Z);break;case"selectionchange":if(ol)break;case"keydown":case"keyup":go(U,n,Z)}var it;if(en)t:{switch(t){case"compositionstart":var at="onCompositionStart";break t;case"compositionend":at="onCompositionEnd";break t;case"compositionupdate":at="onCompositionUpdate";break t}at=void 0}else Bn?al(t,n)&&(at="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(at="onCompositionStart");at&&(Il&&n.locale!=="ko"&&(Bn||at!=="onCompositionStart"?at==="onCompositionEnd"&&Bn&&(it=Dn()):(si=Z,gi="value"in si?si.value:si.textContent,Bn=!0)),pt=au(M,at),0<pt.length&&(at=new Xe(at,t,null,n,Z),U.push({event:at,listeners:pt}),it?at.data=it:(it=mo(n),it!==null&&(at.data=it)))),(it=fo?_o(t,n):ju(t,n))&&(at=au(M,"onBeforeInput"),0<at.length&&(pt=new Xe("onBeforeInput","beforeinput",null,n,Z),U.push({event:pt,listeners:at}),pt.data=it)),um(U,t,M,n,Z)}Dh(U,e)})}function As(t,e,n){return{instance:t,listener:e,currentTarget:n}}function au(t,e){for(var n=e+"Capture",l=[];t!==null;){var o=t,u=o.stateNode;if(o=o.tag,o!==5&&o!==26&&o!==27||u===null||(o=St(t,n),o!=null&&l.unshift(As(t,o,u)),o=St(t,e),o!=null&&l.push(As(t,o,u))),t.tag===3)return l;t=t.return}return[]}function Nl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Bh(t,e,n,l,o){for(var u=e._reactName,f=[];n!==null&&n!==l;){var d=n,p=d.alternate,M=d.stateNode;if(d=d.tag,p!==null&&p===l)break;d!==5&&d!==26&&d!==27||M===null||(p=M,o?(M=St(n,u),M!=null&&f.unshift(As(n,M,p))):o||(M=St(n,u),M!=null&&f.push(As(n,M,p)))),n=n.return}f.length!==0&&t.push({event:e,listeners:f})}var hm=/\r\n?/g,dm=/\u0000|\uFFFD/g;function Zh(t){return(typeof t=="string"?t:""+t).replace(hm,`
`).replace(dm,"")}function jh(t,e){return e=Zh(e),Zh(t)===e}function lu(){}function Ot(t,e,n,l,o,u){switch(n){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||ai(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&ai(t,""+l);break;case"className":Wa(t,"class",l);break;case"tabIndex":Wa(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Wa(t,n,l);break;case"style":An(t,l,u);break;case"data":if(e!=="object"){Wa(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||n!=="href")){t.removeAttribute(n);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=On(""+l),t.setAttribute(n,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(n==="formAction"?(e!=="input"&&Ot(t,e,"name",o.name,o,null),Ot(t,e,"formEncType",o.formEncType,o,null),Ot(t,e,"formMethod",o.formMethod,o,null),Ot(t,e,"formTarget",o.formTarget,o,null)):(Ot(t,e,"encType",o.encType,o,null),Ot(t,e,"method",o.method,o,null),Ot(t,e,"target",o.target,o,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(n);break}l=On(""+l),t.setAttribute(n,l);break;case"onClick":l!=null&&(t.onclick=lu);break;case"onScroll":l!=null&&gt("scroll",t);break;case"onScrollEnd":l!=null&&gt("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(v(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(v(60));t.innerHTML=n}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}n=On(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""+l):t.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,""):t.removeAttribute(n);break;case"capture":case"download":l===!0?t.setAttribute(n,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(n,l):t.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(n,l):t.removeAttribute(n);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(n):t.setAttribute(n,l);break;case"popover":gt("beforetoggle",t),gt("toggle",t),Ja(t,"popover",l);break;case"xlinkActuate":vi(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":vi(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":vi(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":vi(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":vi(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":vi(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":vi(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":vi(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":vi(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Ja(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(n=Yl.get(n)||n,Ja(t,n,l))}}function Fr(t,e,n,l,o,u){switch(n){case"style":An(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(v(61));if(n=l.__html,n!=null){if(o.children!=null)throw Error(v(60));t.innerHTML=n}}break;case"children":typeof l=="string"?ai(t,l):(typeof l=="number"||typeof l=="bigint")&&ai(t,""+l);break;case"onScroll":l!=null&&gt("scroll",t);break;case"onScrollEnd":l!=null&&gt("scrollend",t);break;case"onClick":l!=null&&(t.onclick=lu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!to.hasOwnProperty(n))t:{if(n[0]==="o"&&n[1]==="n"&&(o=n.endsWith("Capture"),e=n.slice(2,o?n.length-7:void 0),u=t[Te]||null,u=u!=null?u[n]:null,typeof u=="function"&&t.removeEventListener(e,u,o),typeof l=="function")){typeof u!="function"&&u!==null&&(n in t?t[n]=null:t.hasAttribute(n)&&t.removeAttribute(n)),t.addEventListener(e,l,o);break t}n in t?t[n]=l:l===!0?t.setAttribute(n,""):Ja(t,n,l)}}}function ye(t,e,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":gt("error",t),gt("load",t);var l=!1,o=!1,u;for(u in n)if(n.hasOwnProperty(u)){var f=n[u];if(f!=null)switch(u){case"src":l=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(v(137,e));default:Ot(t,e,u,f,n,null)}}o&&Ot(t,e,"srcSet",n.srcSet,n,null),l&&Ot(t,e,"src",n.src,n,null);return;case"input":gt("invalid",t);var d=u=f=o=null,p=null,M=null;for(l in n)if(n.hasOwnProperty(l)){var Z=n[l];if(Z!=null)switch(l){case"name":o=Z;break;case"type":f=Z;break;case"checked":p=Z;break;case"defaultChecked":M=Z;break;case"value":u=Z;break;case"defaultValue":d=Z;break;case"children":case"dangerouslySetInnerHTML":if(Z!=null)throw Error(v(137,e));break;default:Ot(t,e,l,Z,n,null)}}io(t,u,d,p,M,f,o,!1),zn(t);return;case"select":gt("invalid",t),l=f=u=null;for(o in n)if(n.hasOwnProperty(o)&&(d=n[o],d!=null))switch(o){case"value":u=d;break;case"defaultValue":f=d;break;case"multiple":l=d;default:Ot(t,e,o,d,n,null)}e=u,n=f,t.multiple=!!l,e!=null?qe(t,!!l,e,!1):n!=null&&qe(t,!!l,n,!0);return;case"textarea":gt("invalid",t),u=o=l=null;for(f in n)if(n.hasOwnProperty(f)&&(d=n[f],d!=null))switch(f){case"value":l=d;break;case"defaultValue":o=d;break;case"children":u=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(v(91));break;default:Ot(t,e,f,d,n,null)}Li(t,l,o,u),zn(t);return;case"option":for(p in n)if(n.hasOwnProperty(p)&&(l=n[p],l!=null))switch(p){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ot(t,e,p,l,n,null)}return;case"dialog":gt("beforetoggle",t),gt("toggle",t),gt("cancel",t),gt("close",t);break;case"iframe":case"object":gt("load",t);break;case"video":case"audio":for(l=0;l<Ls.length;l++)gt(Ls[l],t);break;case"image":gt("error",t),gt("load",t);break;case"details":gt("toggle",t);break;case"embed":case"source":case"link":gt("error",t),gt("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(M in n)if(n.hasOwnProperty(M)&&(l=n[M],l!=null))switch(M){case"children":case"dangerouslySetInnerHTML":throw Error(v(137,e));default:Ot(t,e,M,l,n,null)}return;default:if(ba(e)){for(Z in n)n.hasOwnProperty(Z)&&(l=n[Z],l!==void 0&&Fr(t,e,Z,l,n,void 0));return}}for(d in n)n.hasOwnProperty(d)&&(l=n[d],l!=null&&Ot(t,e,d,l,n,null))}function mm(t,e,n,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,u=null,f=null,d=null,p=null,M=null,Z=null;for(O in n){var U=n[O];if(n.hasOwnProperty(O)&&U!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":p=U;default:l.hasOwnProperty(O)||Ot(t,e,O,null,l,U)}}for(var z in l){var O=l[z];if(U=n[z],l.hasOwnProperty(z)&&(O!=null||U!=null))switch(z){case"type":u=O;break;case"name":o=O;break;case"checked":M=O;break;case"defaultChecked":Z=O;break;case"value":f=O;break;case"defaultValue":d=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(v(137,e));break;default:O!==U&&Ot(t,e,z,O,l,U)}}we(t,f,d,p,M,Z,u,o);return;case"select":O=f=d=z=null;for(u in n)if(p=n[u],n.hasOwnProperty(u)&&p!=null)switch(u){case"value":break;case"multiple":O=p;default:l.hasOwnProperty(u)||Ot(t,e,u,null,l,p)}for(o in l)if(u=l[o],p=n[o],l.hasOwnProperty(o)&&(u!=null||p!=null))switch(o){case"value":z=u;break;case"defaultValue":d=u;break;case"multiple":f=u;default:u!==p&&Ot(t,e,o,u,l,p)}e=d,n=f,l=O,z!=null?qe(t,!!n,z,!1):!!l!=!!n&&(e!=null?qe(t,!!n,e,!0):qe(t,!!n,n?[]:"",!1));return;case"textarea":O=z=null;for(d in n)if(o=n[d],n.hasOwnProperty(d)&&o!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Ot(t,e,d,null,l,o)}for(f in l)if(o=l[f],u=n[f],l.hasOwnProperty(f)&&(o!=null||u!=null))switch(f){case"value":z=o;break;case"defaultValue":O=o;break;case"children":break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(v(91));break;default:o!==u&&Ot(t,e,f,o,l,u)}Gt(t,z,O);return;case"option":for(var lt in n)if(z=n[lt],n.hasOwnProperty(lt)&&z!=null&&!l.hasOwnProperty(lt))switch(lt){case"selected":t.selected=!1;break;default:Ot(t,e,lt,null,l,z)}for(p in l)if(z=l[p],O=n[p],l.hasOwnProperty(p)&&z!==O&&(z!=null||O!=null))switch(p){case"selected":t.selected=z&&typeof z!="function"&&typeof z!="symbol";break;default:Ot(t,e,p,z,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var nt in n)z=n[nt],n.hasOwnProperty(nt)&&z!=null&&!l.hasOwnProperty(nt)&&Ot(t,e,nt,null,l,z);for(M in l)if(z=l[M],O=n[M],l.hasOwnProperty(M)&&z!==O&&(z!=null||O!=null))switch(M){case"children":case"dangerouslySetInnerHTML":if(z!=null)throw Error(v(137,e));break;default:Ot(t,e,M,z,l,O)}return;default:if(ba(e)){for(var Nt in n)z=n[Nt],n.hasOwnProperty(Nt)&&z!==void 0&&!l.hasOwnProperty(Nt)&&Fr(t,e,Nt,void 0,l,z);for(Z in l)z=l[Z],O=n[Z],!l.hasOwnProperty(Z)||z===O||z===void 0&&O===void 0||Fr(t,e,Z,z,l,O);return}}for(var S in n)z=n[S],n.hasOwnProperty(S)&&z!=null&&!l.hasOwnProperty(S)&&Ot(t,e,S,null,l,z);for(U in l)z=l[U],O=n[U],!l.hasOwnProperty(U)||z===O||z==null&&O==null||Ot(t,e,U,z,l,O)}var Ir=null,$r=null;function su(t){return t.nodeType===9?t:t.ownerDocument}function Uh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Hh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function tc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var ec=null;function _m(){var t=window.event;return t&&t.type==="popstate"?t===ec?!1:(ec=t,!0):(ec=null,!1)}var qh=typeof setTimeout=="function"?setTimeout:void 0,pm=typeof clearTimeout=="function"?clearTimeout:void 0,kh=typeof Promise=="function"?Promise:void 0,vm=typeof queueMicrotask=="function"?queueMicrotask:typeof kh<"u"?function(t){return kh.resolve(null).then(t).catch(gm)}:qh;function gm(t){setTimeout(function(){throw t})}function oa(t){return t==="head"}function Ph(t,e){var n=e,l=0,o=0;do{var u=n.nextSibling;if(t.removeChild(n),u&&u.nodeType===8)if(n=u.data,n==="/$"){if(0<l&&8>l){n=l;var f=t.ownerDocument;if(n&1&&Os(f.documentElement),n&2&&Os(f.body),n&4)for(n=f.head,Os(n),f=n.firstChild;f;){var d=f.nextSibling,p=f.nodeName;f[wn]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&f.rel.toLowerCase()==="stylesheet"||n.removeChild(f),f=d}}if(o===0){t.removeChild(u),Us(e);return}o--}else n==="$"||n==="$?"||n==="$!"?o++:l=n.charCodeAt(0)-48;else l=0;n=u}while(n);Us(e)}function ic(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var n=e;switch(e=e.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ic(n),Ka(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(n.rel.toLowerCase()==="stylesheet")continue}t.removeChild(n)}}function ym(t,e,n,l){for(;t.nodeType===1;){var o=n;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[wn])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==o.rel||t.getAttribute("href")!==(o.href==null||o.href===""?null:o.href)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin)||t.getAttribute("title")!==(o.title==null?null:o.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(o.src==null?null:o.src)||t.getAttribute("type")!==(o.type==null?null:o.type)||t.getAttribute("crossorigin")!==(o.crossOrigin==null?null:o.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=o.name==null?null:""+o.name;if(o.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=wi(t.nextSibling),t===null)break}return null}function bm(t,e,n){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!n||(t=wi(t.nextSibling),t===null))return null;return t}function nc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function xm(t,e){var n=t.ownerDocument;if(t.data!=="$?"||n.readyState==="complete")e();else{var l=function(){e(),n.removeEventListener("DOMContentLoaded",l)};n.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function wi(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var ac=null;function Gh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}function Yh(t,e,n){switch(e=su(n),t){case"html":if(t=e.documentElement,!t)throw Error(v(452));return t;case"head":if(t=e.head,!t)throw Error(v(453));return t;case"body":if(t=e.body,!t)throw Error(v(454));return t;default:throw Error(v(451))}}function Os(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);Ka(t)}var di=new Map,Vh=new Set;function ou(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var vn=Q.d;Q.d={f:Sm,r:Tm,D:wm,C:Mm,L:Em,m:zm,X:Am,S:Lm,M:Om};function Sm(){var t=vn.f(),e=Io();return t||e}function Tm(t){var e=Gi(t);e!==null&&e.tag===5&&e.type==="form"?ff(e):vn.r(t)}var Cl=typeof document>"u"?null:document;function Xh(t,e,n){var l=Cl;if(l&&typeof e=="string"&&e){var o=_e(e);o='link[rel="'+t+'"][href="'+o+'"]',typeof n=="string"&&(o+='[crossorigin="'+n+'"]'),Vh.has(o)||(Vh.add(o),t={rel:t,crossOrigin:n,href:e},l.querySelector(o)===null&&(e=l.createElement("link"),ye(e,"link",t),Ft(e),l.head.appendChild(e)))}}function wm(t){vn.D(t),Xh("dns-prefetch",t,null)}function Mm(t,e){vn.C(t,e),Xh("preconnect",t,e)}function Em(t,e,n){vn.L(t,e,n);var l=Cl;if(l&&t&&e){var o='link[rel="preload"][as="'+_e(e)+'"]';e==="image"&&n&&n.imageSrcSet?(o+='[imagesrcset="'+_e(n.imageSrcSet)+'"]',typeof n.imageSizes=="string"&&(o+='[imagesizes="'+_e(n.imageSizes)+'"]')):o+='[href="'+_e(t)+'"]';var u=o;switch(e){case"style":u=Dl(t);break;case"script":u=Rl(t)}di.has(u)||(t=q({rel:"preload",href:e==="image"&&n&&n.imageSrcSet?void 0:t,as:e},n),di.set(u,t),l.querySelector(o)!==null||e==="style"&&l.querySelector(Ns(u))||e==="script"&&l.querySelector(Cs(u))||(e=l.createElement("link"),ye(e,"link",t),Ft(e),l.head.appendChild(e)))}}function zm(t,e){vn.m(t,e);var n=Cl;if(n&&t){var l=e&&typeof e.as=="string"?e.as:"script",o='link[rel="modulepreload"][as="'+_e(l)+'"][href="'+_e(t)+'"]',u=o;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Rl(t)}if(!di.has(u)&&(t=q({rel:"modulepreload",href:t},e),di.set(u,t),n.querySelector(o)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Cs(u)))return}l=n.createElement("link"),ye(l,"link",t),Ft(l),n.head.appendChild(l)}}}function Lm(t,e,n){vn.S(t,e,n);var l=Cl;if(l&&t){var o=Yi(l).hoistableStyles,u=Dl(t);e=e||"default";var f=o.get(u);if(!f){var d={loading:0,preload:null};if(f=l.querySelector(Ns(u)))d.loading=5;else{t=q({rel:"stylesheet",href:t,"data-precedence":e},n),(n=di.get(u))&&lc(t,n);var p=f=l.createElement("link");Ft(p),ye(p,"link",t),p._p=new Promise(function(M,Z){p.onload=M,p.onerror=Z}),p.addEventListener("load",function(){d.loading|=1}),p.addEventListener("error",function(){d.loading|=2}),d.loading|=4,uu(f,e,l)}f={type:"stylesheet",instance:f,count:1,state:d},o.set(u,f)}}}function Am(t,e){vn.X(t,e);var n=Cl;if(n&&t){var l=Yi(n).hoistableScripts,o=Rl(t),u=l.get(o);u||(u=n.querySelector(Cs(o)),u||(t=q({src:t,async:!0},e),(e=di.get(o))&&sc(t,e),u=n.createElement("script"),Ft(u),ye(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Om(t,e){vn.M(t,e);var n=Cl;if(n&&t){var l=Yi(n).hoistableScripts,o=Rl(t),u=l.get(o);u||(u=n.querySelector(Cs(o)),u||(t=q({src:t,async:!0,type:"module"},e),(e=di.get(o))&&sc(t,e),u=n.createElement("script"),Ft(u),ye(u,"link",t),n.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(o,u))}}function Qh(t,e,n,l){var o=(o=X.current)?ou(o):null;if(!o)throw Error(v(446));switch(t){case"meta":case"title":return null;case"style":return typeof n.precedence=="string"&&typeof n.href=="string"?(e=Dl(n.href),n=Yi(o).hoistableStyles,l=n.get(e),l||(l={type:"style",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(n.rel==="stylesheet"&&typeof n.href=="string"&&typeof n.precedence=="string"){t=Dl(n.href);var u=Yi(o).hoistableStyles,f=u.get(t);if(f||(o=o.ownerDocument||o,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,f),(u=o.querySelector(Ns(t)))&&!u._p&&(f.instance=u,f.state.loading=5),di.has(t)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},di.set(t,n),u||Nm(o,t,n,f.state))),e&&l===null)throw Error(v(528,""));return f}if(e&&l!==null)throw Error(v(529,""));return null;case"script":return e=n.async,n=n.src,typeof n=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Rl(n),n=Yi(o).hoistableScripts,l=n.get(e),l||(l={type:"script",instance:null,count:0,state:null},n.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(v(444,t))}}function Dl(t){return'href="'+_e(t)+'"'}function Ns(t){return'link[rel="stylesheet"]['+t+"]"}function Kh(t){return q({},t,{"data-precedence":t.precedence,precedence:null})}function Nm(t,e,n,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),ye(e,"link",n),Ft(e),t.head.appendChild(e))}function Rl(t){return'[src="'+_e(t)+'"]'}function Cs(t){return"script[async]"+t}function Jh(t,e,n){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+_e(n.href)+'"]');if(l)return e.instance=l,Ft(l),l;var o=q({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Ft(l),ye(l,"style",o),uu(l,n.precedence,t),e.instance=l;case"stylesheet":o=Dl(n.href);var u=t.querySelector(Ns(o));if(u)return e.state.loading|=4,e.instance=u,Ft(u),u;l=Kh(n),(o=di.get(o))&&lc(l,o),u=(t.ownerDocument||t).createElement("link"),Ft(u);var f=u;return f._p=new Promise(function(d,p){f.onload=d,f.onerror=p}),ye(u,"link",l),e.state.loading|=4,uu(u,n.precedence,t),e.instance=u;case"script":return u=Rl(n.src),(o=t.querySelector(Cs(u)))?(e.instance=o,Ft(o),o):(l=n,(o=di.get(u))&&(l=q({},n),sc(l,o)),t=t.ownerDocument||t,o=t.createElement("script"),Ft(o),ye(o,"link",l),t.head.appendChild(o),e.instance=o);case"void":return null;default:throw Error(v(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,uu(l,n.precedence,t));return e.instance}function uu(t,e,n){for(var l=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=l.length?l[l.length-1]:null,u=o,f=0;f<l.length;f++){var d=l[f];if(d.dataset.precedence===e)u=d;else if(u!==o)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=n.nodeType===9?n.head:n,e.insertBefore(t,e.firstChild))}function lc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function sc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var ru=null;function Wh(t,e,n){if(ru===null){var l=new Map,o=ru=new Map;o.set(n,l)}else o=ru,l=o.get(n),l||(l=new Map,o.set(n,l));if(l.has(t))return l;for(l.set(t,null),n=n.getElementsByTagName(t),o=0;o<n.length;o++){var u=n[o];if(!(u[wn]||u[le]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(e)||"";f=t+f;var d=l.get(f);d?d.push(u):l.set(f,[u])}}return l}function Fh(t,e,n){t=t.ownerDocument||t,t.head.insertBefore(n,e==="title"?t.querySelector("head > title"):null)}function Cm(t,e,n){if(n===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Ih(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Ds=null;function Dm(){}function Rm(t,e,n){if(Ds===null)throw Error(v(475));var l=Ds;if(e.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var o=Dl(n.href),u=t.querySelector(Ns(o));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=cu.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Ft(u);return}u=t.ownerDocument||t,n=Kh(n),(o=di.get(o))&&lc(n,o),u=u.createElement("link"),Ft(u);var f=u;f._p=new Promise(function(d,p){f.onload=d,f.onerror=p}),ye(u,"link",n),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=cu.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Bm(){if(Ds===null)throw Error(v(475));var t=Ds;return t.stylesheets&&t.count===0&&oc(t,t.stylesheets),0<t.count?function(e){var n=setTimeout(function(){if(t.stylesheets&&oc(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(n)}}:null}function cu(){if(this.count--,this.count===0){if(this.stylesheets)oc(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var fu=null;function oc(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,fu=new Map,e.forEach(Zm,t),fu=null,cu.call(t))}function Zm(t,e){if(!(e.state.loading&4)){var n=fu.get(t);if(n)var l=n.get(null);else{n=new Map,fu.set(t,n);for(var o=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<o.length;u++){var f=o[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(n.set(f.dataset.precedence,f),l=f)}l&&n.set(null,l)}o=e.instance,f=o.getAttribute("data-precedence"),u=n.get(f)||l,u===l&&n.set(null,o),n.set(f,o),this.count++,l=cu.bind(this),o.addEventListener("load",l),o.addEventListener("error",l),u?u.parentNode.insertBefore(o,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(o,t.firstChild)),e.state.loading|=4}}var Rs={$$typeof:wt,Provider:null,Consumer:null,_currentValue:P,_currentValue2:P,_threadCount:0};function jm(t,e,n,l,o,u,f,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Qa(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qa(0),this.hiddenUpdates=Qa(null),this.identifierPrefix=l,this.onUncaughtError=o,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function $h(t,e,n,l,o,u,f,d,p,M,Z,U){return t=new jm(t,e,n,f,d,p,M,U),e=1,u===!0&&(e|=24),u=Ze(3,null,null,e),t.current=u,u.stateNode=t,e=Pu(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:n,cache:e},Xu(u),t}function td(t){return t?(t=un,t):un}function ed(t,e,n,l,o,u){o=td(o),l.context===null?l.context=o:l.pendingContext=o,l=Kn(e),l.payload={element:n},u=u===void 0?null:u,u!==null&&(l.callback=u),n=Jn(t,l,e),n!==null&&(ii(n,t,e),cs(n,t,e))}function id(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function uc(t,e){id(t,e),(t=t.alternate)&&id(t,e)}function nd(t){if(t.tag===13){var e=Gn(t,67108864);e!==null&&ii(e,t,67108864),uc(t,67108864)}}var hu=!0;function Um(t,e,n,l){var o=D.T;D.T=null;var u=Q.p;try{Q.p=2,rc(t,e,n,l)}finally{Q.p=u,D.T=o}}function Hm(t,e,n,l){var o=D.T;D.T=null;var u=Q.p;try{Q.p=8,rc(t,e,n,l)}finally{Q.p=u,D.T=o}}function rc(t,e,n,l){if(hu){var o=cc(l);if(o===null)Wr(t,e,l,du,n),ld(t,l);else if(km(o,t,e,n,l))l.stopPropagation();else if(ld(t,l),e&4&&-1<qm.indexOf(t)){for(;o!==null;){var u=Gi(o);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=Mi(u.pendingLanes);if(f!==0){var d=u;for(d.pendingLanes|=2,d.entangledLanes|=2;f;){var p=1<<31-Oe(f);d.entanglements[1]|=p,f&=~p}Ui(u),(zt&6)===0&&(Wo=Ue()+500,zs(0))}}break;case 13:d=Gn(u,2),d!==null&&ii(d,u,2),Io(),uc(u,2)}if(u=cc(l),u===null&&Wr(t,e,l,du,n),u===o)break;o=u}o!==null&&l.stopPropagation()}else Wr(t,e,l,null,n)}}function cc(t){return t=Nn(t),fc(t)}var du=null;function fc(t){if(du=null,t=zi(t),t!==null){var e=V(t);if(e===null)t=null;else{var n=e.tag;if(n===13){if(t=k(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return du=t,null}function ad(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Va()){case Ys:return 2;case Zl:return 8;case bn:case Vs:return 32;case jl:return 268435456;default:return 32}default:return 32}}var hc=!1,ua=null,ra=null,ca=null,Bs=new Map,Zs=new Map,fa=[],qm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function ld(t,e){switch(t){case"focusin":case"focusout":ua=null;break;case"dragenter":case"dragleave":ra=null;break;case"mouseover":case"mouseout":ca=null;break;case"pointerover":case"pointerout":Bs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zs.delete(e.pointerId)}}function js(t,e,n,l,o,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:n,eventSystemFlags:l,nativeEvent:u,targetContainers:[o]},e!==null&&(e=Gi(e),e!==null&&nd(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function km(t,e,n,l,o){switch(e){case"focusin":return ua=js(ua,t,e,n,l,o),!0;case"dragenter":return ra=js(ra,t,e,n,l,o),!0;case"mouseover":return ca=js(ca,t,e,n,l,o),!0;case"pointerover":var u=o.pointerId;return Bs.set(u,js(Bs.get(u)||null,t,e,n,l,o)),!0;case"gotpointercapture":return u=o.pointerId,Zs.set(u,js(Zs.get(u)||null,t,e,n,l,o)),!0}return!1}function sd(t){var e=zi(t.target);if(e!==null){var n=V(e);if(n!==null){if(e=n.tag,e===13){if(e=k(n),e!==null){t.blockedOn=e,kl(t.priority,function(){if(n.tag===13){var l=ei();l=Hl(l);var o=Gn(n,l);o!==null&&ii(o,n,l),uc(n,l)}});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function mu(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=cc(t.nativeEvent);if(n===null){n=t.nativeEvent;var l=new n.constructor(n.type,n);xa=l,n.target.dispatchEvent(l),xa=null}else return e=Gi(n),e!==null&&nd(e),t.blockedOn=n,!1;e.shift()}return!0}function od(t,e,n){mu(t)&&n.delete(e)}function Pm(){hc=!1,ua!==null&&mu(ua)&&(ua=null),ra!==null&&mu(ra)&&(ra=null),ca!==null&&mu(ca)&&(ca=null),Bs.forEach(od),Zs.forEach(od)}function _u(t,e){t.blockedOn===e&&(t.blockedOn=null,hc||(hc=!0,g.unstable_scheduleCallback(g.unstable_NormalPriority,Pm)))}var pu=null;function ud(t){pu!==t&&(pu=t,g.unstable_scheduleCallback(g.unstable_NormalPriority,function(){pu===t&&(pu=null);for(var e=0;e<t.length;e+=3){var n=t[e],l=t[e+1],o=t[e+2];if(typeof l!="function"){if(fc(l||n)===null)continue;break}var u=Gi(n);u!==null&&(t.splice(e,3),e-=3,fr(u,{pending:!0,data:o,method:n.method,action:l},l,o))}}))}function Us(t){function e(p){return _u(p,t)}ua!==null&&_u(ua,t),ra!==null&&_u(ra,t),ca!==null&&_u(ca,t),Bs.forEach(e),Zs.forEach(e);for(var n=0;n<fa.length;n++){var l=fa[n];l.blockedOn===t&&(l.blockedOn=null)}for(;0<fa.length&&(n=fa[0],n.blockedOn===null);)sd(n),n.blockedOn===null&&fa.shift();if(n=(t.ownerDocument||t).$$reactFormReplay,n!=null)for(l=0;l<n.length;l+=3){var o=n[l],u=n[l+1],f=o[Te]||null;if(typeof u=="function")f||ud(n);else if(f){var d=null;if(u&&u.hasAttribute("formAction")){if(o=u,f=u[Te]||null)d=f.formAction;else if(fc(o)!==null)continue}else d=f.action;typeof d=="function"?n[l+1]=d:(n.splice(l,3),l-=3),ud(n)}}}function dc(t){this._internalRoot=t}vu.prototype.render=dc.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(v(409));var n=e.current,l=ei();ed(n,l,t,e,null,null)},vu.prototype.unmount=dc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;ed(t.current,2,null,t,null,null),Io(),e[Tn]=null}};function vu(t){this._internalRoot=t}vu.prototype.unstable_scheduleHydration=function(t){if(t){var e=Fs();t={blockedOn:null,target:t,priority:e};for(var n=0;n<fa.length&&e!==0&&e<fa[n].priority;n++);fa.splice(n,0,t),n===0&&sd(t)}};var rd=E.version;if(rd!=="19.1.0")throw Error(v(527,rd,"19.1.0"));Q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(v(188)):(t=Object.keys(t).join(","),Error(v(268,t)));return t=A(e),t=t!==null?C(t):null,t=t===null?null:t.stateNode,t};var Gm={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gu.isDisabled&&gu.supportsFiber)try{Pi=gu.inject(Gm),Se=gu}catch{}}return qs.createRoot=function(t,e){if(!B(t))throw Error(v(299));var n=!1,l="",o=Mf,u=Ef,f=zf,d=null;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(o=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=$h(t,1,!1,null,null,n,l,o,u,f,d,null),t[Tn]=e.current,Jr(t),new dc(e)},qs.hydrateRoot=function(t,e,n){if(!B(t))throw Error(v(299));var l=!1,o="",u=Mf,f=Ef,d=zf,p=null,M=null;return n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(o=n.identifierPrefix),n.onUncaughtError!==void 0&&(u=n.onUncaughtError),n.onCaughtError!==void 0&&(f=n.onCaughtError),n.onRecoverableError!==void 0&&(d=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(p=n.unstable_transitionCallbacks),n.formState!==void 0&&(M=n.formState)),e=$h(t,1,!0,e,n??null,l,o,u,f,d,p,M),e.context=td(null),n=e.current,l=ei(),l=Hl(l),o=Kn(l),o.callback=null,Jn(n,o,l),n=l,e.current.lanes=n,Sn(e,n),Ui(e),t[Tn]=e.current,Jr(t),new vu(e)},qs.version="19.1.0",qs}var bd;function $m(){if(bd)return pc.exports;bd=1;function g(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(g)}catch(E){console.error(E)}}return g(),pc.exports=Im(),pc.exports}var t_=$m();/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e_=g=>g.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i_=g=>g.replace(/^([A-Z])|[\s-_]+(\w)/g,(E,b,v)=>v?v.toUpperCase():b.toLowerCase()),xd=g=>{const E=i_(g);return E.charAt(0).toUpperCase()+E.slice(1)},zd=(...g)=>g.filter((E,b,v)=>!!E&&E.trim()!==""&&v.indexOf(E)===b).join(" ").trim(),n_=g=>{for(const E in g)if(E.startsWith("aria-")||E==="role"||E==="title")return!0};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var a_={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l_=ie.forwardRef(({color:g="currentColor",size:E=24,strokeWidth:b=2,absoluteStrokeWidth:v,className:B="",children:V,iconNode:k,...ct},A)=>ie.createElement("svg",{ref:A,...a_,width:E,height:E,stroke:g,strokeWidth:v?Number(b)*24/Number(E):b,className:zd("lucide",B),...!V&&!n_(ct)&&{"aria-hidden":"true"},...ct},[...k.map(([C,q])=>ie.createElement(C,q)),...Array.isArray(V)?V:[V]]));/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Le=(g,E)=>{const b=ie.forwardRef(({className:v,...B},V)=>ie.createElement(l_,{ref:V,iconNode:E,className:zd(`lucide-${e_(xd(g))}`,`lucide-${g}`,v),...B}));return b.displayName=xd(g),b};/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s_=[["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20",key:"oj1oa8"}],["path",{d:"m20 7 2 .5-2 .5",key:"12nv4d"}],["path",{d:"M10 18v3",key:"1yea0a"}],["path",{d:"M14 17.75V21",key:"1pymcb"}],["path",{d:"M7 18a6 6 0 0 0 3.84-10.61",key:"1npnn0"}]],o_=Le("bird",s_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u_=[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]],r_=Le("chart-column",u_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]],f_=Le("circle-question-mark",c_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h_=[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]],d_=Le("eye-off",h_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m_=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ld=Le("eye",m_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const __=[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]],Sd=Le("funnel",__);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],v_=Le("globe",p_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g_=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]],y_=Le("info",g_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b_=[["path",{d:"M10 8h.01",key:"1r9ogq"}],["path",{d:"M12 12h.01",key:"1mp3jc"}],["path",{d:"M14 8h.01",key:"1primd"}],["path",{d:"M16 12h.01",key:"1l6xoz"}],["path",{d:"M18 8h.01",key:"emo2bl"}],["path",{d:"M6 8h.01",key:"x9i8wu"}],["path",{d:"M7 16h10",key:"wp8him"}],["path",{d:"M8 12h.01",key:"czm47f"}],["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}]],x_=Le("keyboard",b_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const S_=[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]],T_=Le("loader-circle",S_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w_=[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]],M_=Le("map-pin",w_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const E_=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],z_=Le("menu",E_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const L_=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Ad=Le("search",L_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A_=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],O_=Le("settings",A_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const N_=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],C_=Le("sparkles",N_);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const D_=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Od=Le("x",D_),R_=()=>x.jsxs("header",{className:"relative bg-gradient-to-r from-primary-600 via-primary-500 to-primary-600 shadow-lg",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-blue-600/20 via-transparent to-blue-600/20"}),x.jsx("div",{className:"absolute inset-0 opacity-10",children:x.jsx("div",{className:"w-full h-full bg-repeat",style:{backgroundImage:`url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`}})}),x.jsx("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:x.jsxs("div",{className:"flex justify-between items-center h-16",children:[x.jsx("div",{className:"flex items-center",children:x.jsxs("div",{className:"flex-shrink-0 flex items-center group",children:[x.jsxs("div",{className:"relative",children:[x.jsx(o_,{className:"h-8 w-8 text-white group-hover:scale-110 transition-transform duration-200"}),x.jsx(C_,{className:"h-3 w-3 text-yellow-300 absolute -top-1 -right-1 animate-pulse"})]}),x.jsxs("span",{className:"ml-3 text-xl font-bold text-white tracking-tight",children:["eBird",x.jsx("span",{className:"text-blue-200 text-sm font-normal ml-1",children:"观鸟热点"})]})]})}),x.jsxs("nav",{className:"hidden md:flex space-x-1",children:[x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"提交记录"}),x.jsx("a",{href:"#",className:"text-white bg-white/20 px-4 py-2 text-sm font-medium rounded-lg backdrop-blur-sm border border-white/30",children:"探索"}),x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"我的eBird"}),x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"科学"}),x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"关于"}),x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"新闻"}),x.jsx("a",{href:"#",className:"text-white/80 hover:text-white hover:bg-white/10 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200",children:"帮助"})]}),x.jsxs("div",{className:"flex items-center space-x-3",children:[x.jsxs("button",{className:"flex items-center text-white/80 hover:text-white hover:bg-white/10 px-3 py-2 rounded-lg transition-all duration-200",children:[x.jsx(v_,{className:"h-4 w-4 mr-2"}),x.jsx("span",{className:"text-sm font-medium",children:"中文"})]}),x.jsx("button",{className:"bg-white text-primary-600 px-6 py-2 rounded-lg text-sm font-semibold hover:bg-blue-50 transition-all duration-200 shadow-soft hover:shadow-medium",children:"登录"}),x.jsx("button",{className:"md:hidden text-white hover:bg-white/10 p-2 rounded-lg transition-all duration-200",children:x.jsx(z_,{className:"h-6 w-6"})})]})]})})]}),Td=g=>{let E;const b=new Set,v=(C,q)=>{const I=typeof C=="function"?C(E):C;if(!Object.is(I,E)){const ut=E;E=q??(typeof I!="object"||I===null)?I:Object.assign({},E,I),b.forEach(kt=>kt(E,ut))}},B=()=>E,ct={setState:v,getState:B,getInitialState:()=>A,subscribe:C=>(b.add(C),()=>b.delete(C))},A=E=g(v,B,ct);return ct},B_=g=>g?Td(g):Td,Z_=g=>g;function j_(g,E=Z_){const b=md.useSyncExternalStore(g.subscribe,()=>E(g.getState()),()=>E(g.getInitialState()));return md.useDebugValue(b),b}const wd=g=>{const E=B_(g),b=v=>j_(E,v);return Object.assign(b,E),b},U_=g=>g?wd(g):wd,bc=[{id:"sparrow",name:"麻雀",scientificName:"Passer domesticus",color:"#FF6B6B",description:"常见的城市鸟类",isVisible:!0},{id:"swallow",name:"燕子",scientificName:"Hirundo rustica",color:"#4ECDC4",description:"迁徙性鸟类",isVisible:!0},{id:"magpie",name:"喜鹊",scientificName:"Pica pica",color:"#45B7D1",description:"智慧的鸟类",isVisible:!0},{id:"pigeon",name:"鸽子",scientificName:"Columba livia",color:"#96CEB4",description:"城市适应性强的鸟类",isVisible:!0},{id:"robin",name:"知更鸟",scientificName:"Erithacus rubecula",color:"#FFEAA7",description:"小型鸣禽",isVisible:!0}],H_=()=>{const g=[],E=[{lat:39.9042,lng:116.4074,name:"北京"},{lat:31.2304,lng:121.4737,name:"上海"},{lat:23.1291,lng:113.2644,name:"广州"},{lat:30.5728,lng:104.0668,name:"成都"},{lat:29.563,lng:106.5516,name:"重庆"},{lat:38.0428,lng:114.5149,name:"石家庄"},{lat:36.0611,lng:103.8343,name:"兰州"},{lat:43.8256,lng:87.6168,name:"乌鲁木齐"}];return bc.forEach(b=>{E.forEach(v=>{for(let B=0;B<Math.random()*20+5;B++){const V=(Math.random()-.5)*2,k=(Math.random()-.5)*2;g.push({lat:v.lat+V,lng:v.lng+k,intensity:Math.random(),count:Math.floor(Math.random()*100)+1,speciesId:b.id})}})}),g},da=U_(g=>({species:[],selectedSpecies:[],searchQuery:"",mapCenter:[39.9042,116.4074],mapZoom:5,selectedMapType:"terrain",hotspots:[],observations:[],setSearchQuery:E=>g({searchQuery:E}),toggleSpeciesVisibility:E=>g(b=>({species:b.species.map(v=>v.id===E?{...v,isVisible:!v.isVisible}:v)})),setSelectedSpecies:E=>g({selectedSpecies:E}),setMapCenter:E=>g({mapCenter:E}),setMapZoom:E=>g({mapZoom:E}),setSelectedMapType:E=>g({selectedMapType:E}),initializeData:()=>{const E=H_();g({species:bc,selectedSpecies:bc.map(b=>b.id),observations:E})}})),q_=()=>{const{searchQuery:g,setSearchQuery:E}=da();return x.jsx("div",{className:"bg-white/95 backdrop-blur-sm border-b border-gray-200/50 px-4 py-6 shadow-soft",children:x.jsx("div",{className:"max-w-7xl mx-auto",children:x.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-center space-y-4 sm:space-y-0 sm:space-x-8",children:[x.jsxs("div",{className:"flex items-center space-x-3",children:[x.jsx("div",{className:"w-1 h-8 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full"}),x.jsx("h1",{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"观鸟热点"})]}),x.jsxs("div",{className:"relative group",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-200"}),x.jsxs("div",{className:"relative",children:[x.jsx(Ad,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 group-hover:text-primary-500 transition-colors duration-200"}),x.jsx("input",{type:"text",placeholder:"搜索鸟类物种…",value:g,onChange:b=>E(b.target.value),className:"pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 w-full sm:w-96 bg-white shadow-soft hover:shadow-medium transition-all duration-200 text-gray-900 placeholder-gray-500"})]})]})]})})})};var ks={exports:{}};/* @preserve
 * Leaflet 1.9.4, a JS library for interactive maps. https://leafletjs.com
 * (c) 2010-2023 Vladimir Agafonkin, (c) 2010-2011 CloudMade
 */var k_=ks.exports,Md;function P_(){return Md||(Md=1,function(g,E){(function(b,v){v(E)})(k_,function(b){var v="1.9.4";function B(i){var a,s,r,c;for(s=1,r=arguments.length;s<r;s++){c=arguments[s];for(a in c)i[a]=c[a]}return i}var V=Object.create||function(){function i(){}return function(a){return i.prototype=a,new i}}();function k(i,a){var s=Array.prototype.slice;if(i.bind)return i.bind.apply(i,s.call(arguments,1));var r=s.call(arguments,2);return function(){return i.apply(a,r.length?r.concat(s.call(arguments)):arguments)}}var ct=0;function A(i){return"_leaflet_id"in i||(i._leaflet_id=++ct),i._leaflet_id}function C(i,a,s){var r,c,h,_;return _=function(){r=!1,c&&(h.apply(s,c),c=!1)},h=function(){r?c=arguments:(i.apply(s,arguments),setTimeout(_,a),r=!0)},h}function q(i,a,s){var r=a[1],c=a[0],h=r-c;return i===r&&s?i:((i-c)%h+h)%h+c}function I(){return!1}function ut(i,a){if(a===!1)return i;var s=Math.pow(10,a===void 0?6:a);return Math.round(i*s)/s}function kt(i){return i.trim?i.trim():i.replace(/^\s+|\s+$/g,"")}function jt(i){return kt(i).split(/\s+/)}function mt(i,a){Object.prototype.hasOwnProperty.call(i,"options")||(i.options=i.options?V(i.options):{});for(var s in a)i.options[s]=a[s];return i.options}function Xt(i,a,s){var r=[];for(var c in i)r.push(encodeURIComponent(s?c.toUpperCase():c)+"="+encodeURIComponent(i[c]));return(!a||a.indexOf("?")===-1?"?":"&")+r.join("&")}var ne=/\{ *([\w_ -]+) *\}/g;function Ae(i,a){return i.replace(ne,function(s,r){var c=a[r];if(c===void 0)throw new Error("No value provided for variable "+s);return typeof c=="function"&&(c=c(a)),c})}var wt=Array.isArray||function(i){return Object.prototype.toString.call(i)==="[object Array]"};function fe(i,a){for(var s=0;s<i.length;s++)if(i[s]===a)return s;return-1}var ht="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";function he(i){return window["webkit"+i]||window["moz"+i]||window["ms"+i]}var be=0;function xe(i){var a=+new Date,s=Math.max(0,16-(a-be));return be=a+s,window.setTimeout(i,s)}var je=window.requestAnimationFrame||he("RequestAnimationFrame")||xe,Hi=window.cancelAnimationFrame||he("CancelAnimationFrame")||he("CancelRequestAnimationFrame")||function(i){window.clearTimeout(i)};function Ut(i,a,s){if(s&&je===xe)i.call(a);else return je.call(window,k(i,a))}function Et(i){i&&Hi.call(window,i)}var qi={__proto__:null,extend:B,create:V,bind:k,get lastId(){return ct},stamp:A,throttle:C,wrapNum:q,falseFn:I,formatNum:ut,trim:kt,splitWords:jt,setOptions:mt,getParamString:Xt,template:Ae,isArray:wt,indexOf:fe,emptyImageUrl:ht,requestFn:je,cancelFn:Hi,requestAnimFrame:Ut,cancelAnimFrame:Et};function de(){}de.extend=function(i){var a=function(){mt(this),this.initialize&&this.initialize.apply(this,arguments),this.callInitHooks()},s=a.__super__=this.prototype,r=V(s);r.constructor=a,a.prototype=r;for(var c in this)Object.prototype.hasOwnProperty.call(this,c)&&c!=="prototype"&&c!=="__super__"&&(a[c]=this[c]);return i.statics&&B(a,i.statics),i.includes&&(ae(i.includes),B.apply(null,[r].concat(i.includes))),B(r,i),delete r.statics,delete r.includes,r.options&&(r.options=s.options?V(s.options):{},B(r.options,i.options)),r._initHooks=[],r.callInitHooks=function(){if(!this._initHooksCalled){s.callInitHooks&&s.callInitHooks.call(this),this._initHooksCalled=!0;for(var h=0,_=r._initHooks.length;h<_;h++)r._initHooks[h].call(this)}},a},de.include=function(i){var a=this.prototype.options;return B(this.prototype,i),i.options&&(this.prototype.options=a,this.mergeOptions(i.options)),this},de.mergeOptions=function(i){return B(this.prototype.options,i),this},de.addInitHook=function(i){var a=Array.prototype.slice.call(arguments,1),s=typeof i=="function"?i:function(){this[i].apply(this,a)};return this.prototype._initHooks=this.prototype._initHooks||[],this.prototype._initHooks.push(s),this};function ae(i){if(!(typeof L>"u"||!L||!L.Mixin)){i=wt(i)?i:[i];for(var a=0;a<i.length;a++)i[a]===L.Mixin.Events&&console.warn("Deprecated include of L.Mixin.Events: this property will be removed in future releases, please inherit from L.Evented instead.",new Error().stack)}}var D={on:function(i,a,s){if(typeof i=="object")for(var r in i)this._on(r,i[r],a);else{i=jt(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s)}return this},off:function(i,a,s){if(!arguments.length)delete this._events;else if(typeof i=="object")for(var r in i)this._off(r,i[r],a);else{i=jt(i);for(var c=arguments.length===1,h=0,_=i.length;h<_;h++)c?this._off(i[h]):this._off(i[h],a,s)}return this},_on:function(i,a,s,r){if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}if(this._listens(i,a,s)===!1){s===this&&(s=void 0);var c={fn:a,ctx:s};r&&(c.once=!0),this._events=this._events||{},this._events[i]=this._events[i]||[],this._events[i].push(c)}},_off:function(i,a,s){var r,c,h;if(this._events&&(r=this._events[i],!!r)){if(arguments.length===1){if(this._firingCount)for(c=0,h=r.length;c<h;c++)r[c].fn=I;delete this._events[i];return}if(typeof a!="function"){console.warn("wrong listener type: "+typeof a);return}var _=this._listens(i,a,s);if(_!==!1){var T=r[_];this._firingCount&&(T.fn=I,this._events[i]=r=r.slice()),r.splice(_,1)}}},fire:function(i,a,s){if(!this.listens(i,s))return this;var r=B({},a,{type:i,target:this,sourceTarget:a&&a.sourceTarget||this});if(this._events){var c=this._events[i];if(c){this._firingCount=this._firingCount+1||1;for(var h=0,_=c.length;h<_;h++){var T=c[h],N=T.fn;T.once&&this.off(i,N,T.ctx),N.call(T.ctx||this,r)}this._firingCount--}}return s&&this._propagateEvent(r),this},listens:function(i,a,s,r){typeof i!="string"&&console.warn('"string" type argument expected');var c=a;typeof a!="function"&&(r=!!a,c=void 0,s=void 0);var h=this._events&&this._events[i];if(h&&h.length&&this._listens(i,c,s)!==!1)return!0;if(r){for(var _ in this._eventParents)if(this._eventParents[_].listens(i,a,s,r))return!0}return!1},_listens:function(i,a,s){if(!this._events)return!1;var r=this._events[i]||[];if(!a)return!!r.length;s===this&&(s=void 0);for(var c=0,h=r.length;c<h;c++)if(r[c].fn===a&&r[c].ctx===s)return c;return!1},once:function(i,a,s){if(typeof i=="object")for(var r in i)this._on(r,i[r],a,!0);else{i=jt(i);for(var c=0,h=i.length;c<h;c++)this._on(i[c],a,s,!0)}return this},addEventParent:function(i){return this._eventParents=this._eventParents||{},this._eventParents[A(i)]=i,this},removeEventParent:function(i){return this._eventParents&&delete this._eventParents[A(i)],this},_propagateEvent:function(i){for(var a in this._eventParents)this._eventParents[a].fire(i.type,B({layer:i.target,propagatedFrom:i.target},i),!0)}};D.addEventListener=D.on,D.removeEventListener=D.clearAllEventListeners=D.off,D.addOneTimeEventListener=D.once,D.fireEvent=D.fire,D.hasEventListeners=D.listens;var Q=de.extend(D);function P(i,a,s){this.x=s?Math.round(i):i,this.y=s?Math.round(a):a}var Mt=Math.trunc||function(i){return i>0?Math.floor(i):Math.ceil(i)};P.prototype={clone:function(){return new P(this.x,this.y)},add:function(i){return this.clone()._add(m(i))},_add:function(i){return this.x+=i.x,this.y+=i.y,this},subtract:function(i){return this.clone()._subtract(m(i))},_subtract:function(i){return this.x-=i.x,this.y-=i.y,this},divideBy:function(i){return this.clone()._divideBy(i)},_divideBy:function(i){return this.x/=i,this.y/=i,this},multiplyBy:function(i){return this.clone()._multiplyBy(i)},_multiplyBy:function(i){return this.x*=i,this.y*=i,this},scaleBy:function(i){return new P(this.x*i.x,this.y*i.y)},unscaleBy:function(i){return new P(this.x/i.x,this.y/i.y)},round:function(){return this.clone()._round()},_round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},floor:function(){return this.clone()._floor()},_floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.clone()._ceil()},_ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},trunc:function(){return this.clone()._trunc()},_trunc:function(){return this.x=Mt(this.x),this.y=Mt(this.y),this},distanceTo:function(i){i=m(i);var a=i.x-this.x,s=i.y-this.y;return Math.sqrt(a*a+s*s)},equals:function(i){return i=m(i),i.x===this.x&&i.y===this.y},contains:function(i){return i=m(i),Math.abs(i.x)<=Math.abs(this.x)&&Math.abs(i.y)<=Math.abs(this.y)},toString:function(){return"Point("+ut(this.x)+", "+ut(this.y)+")"}};function m(i,a,s){return i instanceof P?i:wt(i)?new P(i[0],i[1]):i==null?i:typeof i=="object"&&"x"in i&&"y"in i?new P(i.x,i.y):new P(i,a,s)}function R(i,a){if(i)for(var s=a?[i,a]:i,r=0,c=s.length;r<c;r++)this.extend(s[r])}R.prototype={extend:function(i){var a,s;if(!i)return this;if(i instanceof P||typeof i[0]=="number"||"x"in i)a=s=m(i);else if(i=Y(i),a=i.min,s=i.max,!a||!s)return this;return!this.min&&!this.max?(this.min=a.clone(),this.max=s.clone()):(this.min.x=Math.min(a.x,this.min.x),this.max.x=Math.max(s.x,this.max.x),this.min.y=Math.min(a.y,this.min.y),this.max.y=Math.max(s.y,this.max.y)),this},getCenter:function(i){return m((this.min.x+this.max.x)/2,(this.min.y+this.max.y)/2,i)},getBottomLeft:function(){return m(this.min.x,this.max.y)},getTopRight:function(){return m(this.max.x,this.min.y)},getTopLeft:function(){return this.min},getBottomRight:function(){return this.max},getSize:function(){return this.max.subtract(this.min)},contains:function(i){var a,s;return typeof i[0]=="number"||i instanceof P?i=m(i):i=Y(i),i instanceof R?(a=i.min,s=i.max):a=s=i,a.x>=this.min.x&&s.x<=this.max.x&&a.y>=this.min.y&&s.y<=this.max.y},intersects:function(i){i=Y(i);var a=this.min,s=this.max,r=i.min,c=i.max,h=c.x>=a.x&&r.x<=s.x,_=c.y>=a.y&&r.y<=s.y;return h&&_},overlaps:function(i){i=Y(i);var a=this.min,s=this.max,r=i.min,c=i.max,h=c.x>a.x&&r.x<s.x,_=c.y>a.y&&r.y<s.y;return h&&_},isValid:function(){return!!(this.min&&this.max)},pad:function(i){var a=this.min,s=this.max,r=Math.abs(a.x-s.x)*i,c=Math.abs(a.y-s.y)*i;return Y(m(a.x-r,a.y-c),m(s.x+r,s.y+c))},equals:function(i){return i?(i=Y(i),this.min.equals(i.getTopLeft())&&this.max.equals(i.getBottomRight())):!1}};function Y(i,a){return!i||i instanceof R?i:new R(i,a)}function G(i,a){if(i)for(var s=a?[i,a]:i,r=0,c=s.length;r<c;r++)this.extend(s[r])}G.prototype={extend:function(i){var a=this._southWest,s=this._northEast,r,c;if(i instanceof $)r=i,c=i;else if(i instanceof G){if(r=i._southWest,c=i._northEast,!r||!c)return this}else return i?this.extend(X(i)||J(i)):this;return!a&&!s?(this._southWest=new $(r.lat,r.lng),this._northEast=new $(c.lat,c.lng)):(a.lat=Math.min(r.lat,a.lat),a.lng=Math.min(r.lng,a.lng),s.lat=Math.max(c.lat,s.lat),s.lng=Math.max(c.lng,s.lng)),this},pad:function(i){var a=this._southWest,s=this._northEast,r=Math.abs(a.lat-s.lat)*i,c=Math.abs(a.lng-s.lng)*i;return new G(new $(a.lat-r,a.lng-c),new $(s.lat+r,s.lng+c))},getCenter:function(){return new $((this._southWest.lat+this._northEast.lat)/2,(this._southWest.lng+this._northEast.lng)/2)},getSouthWest:function(){return this._southWest},getNorthEast:function(){return this._northEast},getNorthWest:function(){return new $(this.getNorth(),this.getWest())},getSouthEast:function(){return new $(this.getSouth(),this.getEast())},getWest:function(){return this._southWest.lng},getSouth:function(){return this._southWest.lat},getEast:function(){return this._northEast.lng},getNorth:function(){return this._northEast.lat},contains:function(i){typeof i[0]=="number"||i instanceof $||"lat"in i?i=X(i):i=J(i);var a=this._southWest,s=this._northEast,r,c;return i instanceof G?(r=i.getSouthWest(),c=i.getNorthEast()):r=c=i,r.lat>=a.lat&&c.lat<=s.lat&&r.lng>=a.lng&&c.lng<=s.lng},intersects:function(i){i=J(i);var a=this._southWest,s=this._northEast,r=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>=a.lat&&r.lat<=s.lat,_=c.lng>=a.lng&&r.lng<=s.lng;return h&&_},overlaps:function(i){i=J(i);var a=this._southWest,s=this._northEast,r=i.getSouthWest(),c=i.getNorthEast(),h=c.lat>a.lat&&r.lat<s.lat,_=c.lng>a.lng&&r.lng<s.lng;return h&&_},toBBoxString:function(){return[this.getWest(),this.getSouth(),this.getEast(),this.getNorth()].join(",")},equals:function(i,a){return i?(i=J(i),this._southWest.equals(i.getSouthWest(),a)&&this._northEast.equals(i.getNorthEast(),a)):!1},isValid:function(){return!!(this._southWest&&this._northEast)}};function J(i,a){return i instanceof G?i:new G(i,a)}function $(i,a,s){if(isNaN(i)||isNaN(a))throw new Error("Invalid LatLng object: ("+i+", "+a+")");this.lat=+i,this.lng=+a,s!==void 0&&(this.alt=+s)}$.prototype={equals:function(i,a){if(!i)return!1;i=X(i);var s=Math.max(Math.abs(this.lat-i.lat),Math.abs(this.lng-i.lng));return s<=(a===void 0?1e-9:a)},toString:function(i){return"LatLng("+ut(this.lat,i)+", "+ut(this.lng,i)+")"},distanceTo:function(i){return xt.distance(this,X(i))},wrap:function(){return xt.wrapLatLng(this)},toBounds:function(i){var a=180*i/40075017,s=a/Math.cos(Math.PI/180*this.lat);return J([this.lat-a,this.lng-s],[this.lat+a,this.lng+s])},clone:function(){return new $(this.lat,this.lng,this.alt)}};function X(i,a,s){return i instanceof $?i:wt(i)&&typeof i[0]!="object"?i.length===3?new $(i[0],i[1],i[2]):i.length===2?new $(i[0],i[1]):null:i==null?i:typeof i=="object"&&"lat"in i?new $(i.lat,"lng"in i?i.lng:i.lon,i.alt):a===void 0?null:new $(i,a,s)}var Pt={latLngToPoint:function(i,a){var s=this.projection.project(i),r=this.scale(a);return this.transformation._transform(s,r)},pointToLatLng:function(i,a){var s=this.scale(a),r=this.transformation.untransform(i,s);return this.projection.unproject(r)},project:function(i){return this.projection.project(i)},unproject:function(i){return this.projection.unproject(i)},scale:function(i){return 256*Math.pow(2,i)},zoom:function(i){return Math.log(i/256)/Math.LN2},getProjectedBounds:function(i){if(this.infinite)return null;var a=this.projection.bounds,s=this.scale(i),r=this.transformation.transform(a.min,s),c=this.transformation.transform(a.max,s);return new R(r,c)},infinite:!1,wrapLatLng:function(i){var a=this.wrapLng?q(i.lng,this.wrapLng,!0):i.lng,s=this.wrapLat?q(i.lat,this.wrapLat,!0):i.lat,r=i.alt;return new $(s,a,r)},wrapLatLngBounds:function(i){var a=i.getCenter(),s=this.wrapLatLng(a),r=a.lat-s.lat,c=a.lng-s.lng;if(r===0&&c===0)return i;var h=i.getSouthWest(),_=i.getNorthEast(),T=new $(h.lat-r,h.lng-c),N=new $(_.lat-r,_.lng-c);return new G(T,N)}},xt=B({},Pt,{wrapLng:[-180,180],R:6371e3,distance:function(i,a){var s=Math.PI/180,r=i.lat*s,c=a.lat*s,h=Math.sin((a.lat-i.lat)*s/2),_=Math.sin((a.lng-i.lng)*s/2),T=h*h+Math.cos(r)*Math.cos(c)*_*_,N=2*Math.atan2(Math.sqrt(T),Math.sqrt(1-T));return this.R*N}}),mi=6378137,ma={R:mi,MAX_LATITUDE:85.0511287798,project:function(i){var a=Math.PI/180,s=this.MAX_LATITUDE,r=Math.max(Math.min(s,i.lat),-s),c=Math.sin(r*a);return new P(this.R*i.lng*a,this.R*Math.log((1+c)/(1-c))/2)},unproject:function(i){var a=180/Math.PI;return new $((2*Math.atan(Math.exp(i.y/this.R))-Math.PI/2)*a,i.x*a/this.R)},bounds:function(){var i=mi*Math.PI;return new R([-i,-i],[i,i])}()};function yn(i,a,s,r){if(wt(i)){this._a=i[0],this._b=i[1],this._c=i[2],this._d=i[3];return}this._a=i,this._b=a,this._c=s,this._d=r}yn.prototype={transform:function(i,a){return this._transform(i.clone(),a)},_transform:function(i,a){return a=a||1,i.x=a*(this._a*i.x+this._b),i.y=a*(this._c*i.y+this._d),i},untransform:function(i,a){return a=a||1,new P((i.x/a-this._b)/this._a,(i.y/a-this._d)/this._c)}};function ki(i,a,s,r){return new yn(i,a,s,r)}var _a=B({},xt,{code:"EPSG:3857",projection:ma,transformation:function(){var i=.5/(Math.PI*ma.R);return ki(i,.5,-i,.5)}()}),Bl=B({},_a,{code:"EPSG:900913"});function Ps(i){return document.createElementNS("http://www.w3.org/2000/svg",i)}function Gs(i,a){var s="",r,c,h,_,T,N;for(r=0,h=i.length;r<h;r++){for(T=i[r],c=0,_=T.length;c<_;c++)N=T[c],s+=(c?"L":"M")+N.x+" "+N.y;s+=a?et.svg?"z":"x":""}return s||"M0 0"}var Ue=document.documentElement.style,Va="ActiveXObject"in window,Ys=Va&&!document.addEventListener,Zl="msLaunchUri"in navigator&&!("documentMode"in document),bn=He("webkit"),Vs=He("android"),jl=He("android 2")||He("android 3"),yu=parseInt(/WebKit\/([0-9]+)|$/.exec(navigator.userAgent)[1],10),bu=Vs&&He("Google")&&yu<537&&!("AudioNode"in window),Pi=!!window.opera,Se=!Zl&&He("chrome"),_i=He("gecko")&&!bn&&!Pi&&!Va,Oe=!Se&&He("safari"),Xs=He("phantom"),Qs="OTransition"in Ue,xu=navigator.platform.indexOf("Win")===0,pa=Va&&"transition"in Ue,xn="WebKitCSSMatrix"in window&&"m11"in new window.WebKitCSSMatrix&&!jl,Mi="MozPerspective"in Ue,Xa=!window.L_DISABLE_3D&&(pa||xn||Mi)&&!Qs&&!Xs,pi=typeof orientation<"u"||He("mobile"),Su=pi&&bn,Ks=pi&&xn,Ul=!window.PointerEvent&&window.MSPointerEvent,Qa=!!(window.PointerEvent||Ul),Sn="ontouchstart"in window||!!window.TouchEvent,Tu=!window.L_NO_TOUCH&&(Sn||Qa),Js=pi&&Pi,Ws=pi&&_i,Hl=(window.devicePixelRatio||window.screen.deviceXDPI/window.screen.logicalXDPI)>1,ql=function(){var i=!1;try{var a=Object.defineProperty({},"passive",{get:function(){i=!0}});window.addEventListener("testPassiveEventSupport",I,a),window.removeEventListener("testPassiveEventSupport",I,a)}catch{}return i}(),Fs=function(){return!!document.createElement("canvas").getContext}(),kl=!!(document.createElementNS&&Ps("svg").createSVGRect),Ei=!!kl&&function(){var i=document.createElement("div");return i.innerHTML="<svg/>",(i.firstChild&&i.firstChild.namespaceURI)==="http://www.w3.org/2000/svg"}(),le=!kl&&function(){try{var i=document.createElement("div");i.innerHTML='<v:shape adj="1"/>';var a=i.firstChild;return a.style.behavior="url(#default#VML)",a&&typeof a.adj=="object"}catch{return!1}}(),Te=navigator.platform.indexOf("Mac")===0,Tn=navigator.platform.indexOf("Linux")===0;function He(i){return navigator.userAgent.toLowerCase().indexOf(i)>=0}var et={ie:Va,ielt9:Ys,edge:Zl,webkit:bn,android:Vs,android23:jl,androidStock:bu,opera:Pi,chrome:Se,gecko:_i,safari:Oe,phantom:Xs,opera12:Qs,win:xu,ie3d:pa,webkit3d:xn,gecko3d:Mi,any3d:Xa,mobile:pi,mobileWebkit:Su,mobileWebkit3d:Ks,msPointer:Ul,pointer:Qa,touch:Tu,touchNative:Sn,mobileOpera:Js,mobileGecko:Ws,retina:Hl,passiveEvents:ql,canvas:Fs,svg:kl,vml:le,inlineSvg:Ei,mac:Te,linux:Tn},Is=et.msPointer?"MSPointerDown":"pointerdown",Pl=et.msPointer?"MSPointerMove":"pointermove",wn=et.msPointer?"MSPointerUp":"pointerup",Ka=et.msPointer?"MSPointerCancel":"pointercancel",zi={touchstart:Is,touchmove:Pl,touchend:wn,touchcancel:Ka},Gi={touchstart:eo,touchmove:va,touchend:va,touchcancel:va},ni={},Yi=!1;function Ft(i,a,s){return a==="touchstart"&&wu(),Gi[a]?(s=Gi[a].bind(this,s),i.addEventListener(zi[a],s,!1),s):(console.warn("wrong event specified:",a),I)}function $s(i,a,s){if(!zi[a]){console.warn("wrong event specified:",a);return}i.removeEventListener(zi[a],s,!1)}function to(i){ni[i.pointerId]=i}function Vi(i){ni[i.pointerId]&&(ni[i.pointerId]=i)}function Xi(i){delete ni[i.pointerId]}function wu(){Yi||(document.addEventListener(Is,to,!0),document.addEventListener(Pl,Vi,!0),document.addEventListener(wn,Xi,!0),document.addEventListener(Ka,Xi,!0),Yi=!0)}function va(i,a){if(a.pointerType!==(a.MSPOINTER_TYPE_MOUSE||"mouse")){a.touches=[];for(var s in ni)a.touches.push(ni[s]);a.changedTouches=[a],i(a)}}function eo(i,a){a.MSPOINTER_TYPE_TOUCH&&a.pointerType===a.MSPOINTER_TYPE_TOUCH&&Qt(a),va(i,a)}function Mu(i){var a={},s,r;for(r in i)s=i[r],a[r]=s&&s.bind?s.bind(i):s;return i=a,a.type="dblclick",a.detail=2,a.isTrusted=!1,a._simulated=!0,a}var Ja=200;function Wa(i,a){i.addEventListener("dblclick",a);var s=0,r;function c(h){if(h.detail!==1){r=h.detail;return}if(!(h.pointerType==="mouse"||h.sourceCapabilities&&!h.sourceCapabilities.firesTouchEvents)){var _=Vl(h);if(!(_.some(function(N){return N instanceof HTMLLabelElement&&N.attributes.for})&&!_.some(function(N){return N instanceof HTMLInputElement||N instanceof HTMLSelectElement}))){var T=Date.now();T-s<=Ja?(r++,r===2&&a(Mu(h))):r=1,s=T}}}return i.addEventListener("click",c),{dblclick:a,simDblclick:c}}function vi(i,a){i.removeEventListener("dblclick",a.dblclick),i.removeEventListener("click",a.simDblclick)}var ga=Ln(["transform","webkitTransform","OTransform","MozTransform","msTransform"]),Mn=Ln(["webkitTransition","transition","OTransition","MozTransition","msTransition"]),Qi=Mn==="webkitTransition"||Mn==="OTransition"?Mn+"End":"transitionend";function Fa(i){return typeof i=="string"?document.getElementById(i):i}function Ki(i,a){var s=i.style[a]||i.currentStyle&&i.currentStyle[a];if((!s||s==="auto")&&document.defaultView){var r=document.defaultView.getComputedStyle(i,null);s=r?r[a]:null}return s==="auto"?null:s}function yt(i,a,s){var r=document.createElement(i);return r.className=a||"",s&&s.appendChild(r),r}function Dt(i){var a=i.parentNode;a&&a.removeChild(i)}function me(i){for(;i.firstChild;)i.removeChild(i.firstChild)}function Ji(i){var a=i.parentNode;a&&a.lastChild!==i&&a.appendChild(i)}function En(i){var a=i.parentNode;a&&a.firstChild!==i&&a.insertBefore(i,a.firstChild)}function zn(i,a){if(i.classList!==void 0)return i.classList.contains(a);var s=_e(i);return s.length>0&&new RegExp("(^|\\s)"+a+"(\\s|$)").test(s)}function rt(i,a){if(i.classList!==void 0)for(var s=jt(a),r=0,c=s.length;r<c;r++)i.classList.add(s[r]);else if(!zn(i,a)){var h=_e(i);Gl(i,(h?h+" ":"")+a)}}function Rt(i,a){i.classList!==void 0?i.classList.remove(a):Gl(i,kt((" "+_e(i)+" ").replace(" "+a+" "," ")))}function Gl(i,a){i.className.baseVal===void 0?i.className=a:i.className.baseVal=a}function _e(i){return i.correspondingElement&&(i=i.correspondingElement),i.className.baseVal===void 0?i.className:i.className.baseVal}function we(i,a){"opacity"in i.style?i.style.opacity=a:"filter"in i.style&&io(i,a)}function io(i,a){var s=!1,r="DXImageTransform.Microsoft.Alpha";try{s=i.filters.item(r)}catch{if(a===1)return}a=Math.round(a*100),s?(s.Enabled=a!==100,s.Opacity=a):i.style.filter+=" progid:"+r+"(opacity="+a+")"}function Ln(i){for(var a=document.documentElement.style,s=0;s<i.length;s++)if(i[s]in a)return i[s];return!1}function qe(i,a,s){var r=a||new P(0,0);i.style[ga]=(et.ie3d?"translate("+r.x+"px,"+r.y+"px)":"translate3d("+r.x+"px,"+r.y+"px,0)")+(s?" scale("+s+")":"")}function Gt(i,a){i._leaflet_pos=a,et.any3d?qe(i,a):(i.style.left=a.x+"px",i.style.top=a.y+"px")}function Li(i){return i._leaflet_pos||new P(0,0)}var ai,ya,Ia;if("onselectstart"in document)ai=function(){ot(window,"selectstart",Qt)},ya=function(){St(window,"selectstart",Qt)};else{var An=Ln(["userSelect","WebkitUserSelect","OUserSelect","MozUserSelect","msUserSelect"]);ai=function(){if(An){var i=document.documentElement.style;Ia=i[An],i[An]="none"}},ya=function(){An&&(document.documentElement.style[An]=Ia,Ia=void 0)}}function ba(){ot(window,"dragstart",Qt)}function Yl(){St(window,"dragstart",Qt)}var $a,On;function xa(i){for(;i.tabIndex===-1;)i=i.parentNode;i.style&&(Nn(),$a=i,On=i.style.outlineStyle,i.style.outlineStyle="none",ot(window,"keydown",Nn))}function Nn(){$a&&($a.style.outlineStyle=On,$a=void 0,On=void 0,St(window,"keydown",Nn))}function Wi(i){do i=i.parentNode;while((!i.offsetWidth||!i.offsetHeight)&&i!==document.body);return i}function Ai(i){var a=i.getBoundingClientRect();return{x:a.width/i.offsetWidth||1,y:a.height/i.offsetHeight||1,boundingClientRect:a}}var no={__proto__:null,TRANSFORM:ga,TRANSITION:Mn,TRANSITION_END:Qi,get:Fa,getStyle:Ki,create:yt,remove:Dt,empty:me,toFront:Ji,toBack:En,hasClass:zn,addClass:rt,removeClass:Rt,setClass:Gl,getClass:_e,setOpacity:we,testProp:Ln,setTransform:qe,setPosition:Gt,getPosition:Li,get disableTextSelection(){return ai},get enableTextSelection(){return ya},disableImageDrag:ba,enableImageDrag:Yl,preventOutline:xa,restoreOutline:Nn,getSizedParentNode:Wi,getScale:Ai};function ot(i,a,s,r){if(a&&typeof a=="object")for(var c in a)Fi(i,c,a[c],s);else{a=jt(a);for(var h=0,_=a.length;h<_;h++)Fi(i,a[h],s,r)}return this}var Ve="_leaflet_events";function St(i,a,s,r){if(arguments.length===1)li(i),delete i[Ve];else if(a&&typeof a=="object")for(var c in a)si(i,c,a[c],s);else if(a=jt(a),arguments.length===2)li(i,function(T){return fe(a,T)!==-1});else for(var h=0,_=a.length;h<_;h++)si(i,a[h],s,r);return this}function li(i,a){for(var s in i[Ve]){var r=s.split(/\d/)[0];(!a||a(r))&&si(i,r,null,null,s)}}var Sa={mouseenter:"mouseover",mouseleave:"mouseout",wheel:!("onwheel"in window)&&"mousewheel"};function Fi(i,a,s,r){var c=a+A(s)+(r?"_"+A(r):"");if(i[Ve]&&i[Ve][c])return this;var h=function(T){return s.call(r||i,T||window.event)},_=h;!et.touchNative&&et.pointer&&a.indexOf("touch")===0?h=Ft(i,a,h):et.touch&&a==="dblclick"?h=Wa(i,h):"addEventListener"in i?a==="touchstart"||a==="touchmove"||a==="wheel"||a==="mousewheel"?i.addEventListener(Sa[a]||a,h,et.passiveEvents?{passive:!1}:!1):a==="mouseenter"||a==="mouseleave"?(h=function(T){T=T||window.event,$i(i,T)&&_(T)},i.addEventListener(Sa[a],h,!1)):i.addEventListener(a,_,!1):i.attachEvent("on"+a,h),i[Ve]=i[Ve]||{},i[Ve][c]=h}function si(i,a,s,r,c){c=c||a+A(s)+(r?"_"+A(r):"");var h=i[Ve]&&i[Ve][c];if(!h)return this;!et.touchNative&&et.pointer&&a.indexOf("touch")===0?$s(i,a,h):et.touch&&a==="dblclick"?vi(i,h):"removeEventListener"in i?i.removeEventListener(Sa[a]||a,h,!1):i.detachEvent("on"+a,h),i[Ve][c]=null}function gi(i){return i.stopPropagation?i.stopPropagation():i.originalEvent?i.originalEvent._stopped=!0:i.cancelBubble=!0,this}function Cn(i){return Fi(i,"wheel",gi),this}function Dn(i){return ot(i,"mousedown touchstart dblclick contextmenu",gi),i._leaflet_disable_click=!0,this}function Qt(i){return i.preventDefault?i.preventDefault():i.returnValue=!1,this}function oi(i){return Qt(i),gi(i),this}function Vl(i){if(i.composedPath)return i.composedPath();for(var a=[],s=i.target;s;)a.push(s),s=s.parentNode;return a}function pe(i,a){if(!a)return new P(i.clientX,i.clientY);var s=Ai(a),r=s.boundingClientRect;return new P((i.clientX-r.left)/s.x-a.clientLeft,(i.clientY-r.top)/s.y-a.clientTop)}var Ii=et.linux&&et.chrome?window.devicePixelRatio:et.mac?window.devicePixelRatio*3:window.devicePixelRatio>0?2*window.devicePixelRatio:1;function Ta(i){return et.edge?i.wheelDeltaY/2:i.deltaY&&i.deltaMode===0?-i.deltaY/Ii:i.deltaY&&i.deltaMode===1?-i.deltaY*20:i.deltaY&&i.deltaMode===2?-i.deltaY*60:i.deltaX||i.deltaZ?0:i.wheelDelta?(i.wheelDeltaY||i.wheelDelta)/2:i.detail&&Math.abs(i.detail)<32765?-i.detail*20:i.detail?i.detail/-32765*60:0}function $i(i,a){var s=a.relatedTarget;if(!s)return!0;try{for(;s&&s!==i;)s=s.parentNode}catch{return!1}return s!==i}var Eu={__proto__:null,on:ot,off:St,stopPropagation:gi,disableScrollPropagation:Cn,disableClickPropagation:Dn,preventDefault:Qt,stop:oi,getPropagationPath:Vl,getMousePosition:pe,getWheelDelta:Ta,isExternalTarget:$i,addListener:ot,removeListener:St},tl=Q.extend({run:function(i,a,s,r){this.stop(),this._el=i,this._inProgress=!0,this._duration=s||.25,this._easeOutPower=1/Math.max(r||.5,.2),this._startPos=Li(i),this._offset=a.subtract(this._startPos),this._startTime=+new Date,this.fire("start"),this._animate()},stop:function(){this._inProgress&&(this._step(!0),this._complete())},_animate:function(){this._animId=Ut(this._animate,this),this._step()},_step:function(i){var a=+new Date-this._startTime,s=this._duration*1e3;a<s?this._runFrame(this._easeOut(a/s),i):(this._runFrame(1),this._complete())},_runFrame:function(i,a){var s=this._startPos.add(this._offset.multiplyBy(i));a&&s._round(),Gt(this._el,s),this.fire("step")},_complete:function(){Et(this._animId),this._inProgress=!1,this.fire("end")},_easeOut:function(i){return 1-Math.pow(1-i,this._easeOutPower)}}),_t=Q.extend({options:{crs:_a,center:void 0,zoom:void 0,minZoom:void 0,maxZoom:void 0,layers:[],maxBounds:void 0,renderer:void 0,zoomAnimation:!0,zoomAnimationThreshold:4,fadeAnimation:!0,markerZoomAnimation:!0,transform3DLimit:8388608,zoomSnap:1,zoomDelta:1,trackResize:!0},initialize:function(i,a){a=mt(this,a),this._handlers=[],this._layers={},this._zoomBoundLayers={},this._sizeChanged=!0,this._initContainer(i),this._initLayout(),this._onResize=k(this._onResize,this),this._initEvents(),a.maxBounds&&this.setMaxBounds(a.maxBounds),a.zoom!==void 0&&(this._zoom=this._limitZoom(a.zoom)),a.center&&a.zoom!==void 0&&this.setView(X(a.center),a.zoom,{reset:!0}),this.callInitHooks(),this._zoomAnimated=Mn&&et.any3d&&!et.mobileOpera&&this.options.zoomAnimation,this._zoomAnimated&&(this._createAnimProxy(),ot(this._proxy,Qi,this._catchTransitionEnd,this)),this._addLayers(this.options.layers)},setView:function(i,a,s){if(a=a===void 0?this._zoom:this._limitZoom(a),i=this._limitCenter(X(i),a,this.options.maxBounds),s=s||{},this._stop(),this._loaded&&!s.reset&&s!==!0){s.animate!==void 0&&(s.zoom=B({animate:s.animate},s.zoom),s.pan=B({animate:s.animate,duration:s.duration},s.pan));var r=this._zoom!==a?this._tryAnimatedZoom&&this._tryAnimatedZoom(i,a,s.zoom):this._tryAnimatedPan(i,s.pan);if(r)return clearTimeout(this._sizeTimer),this}return this._resetView(i,a,s.pan&&s.pan.noMoveStart),this},setZoom:function(i,a){return this._loaded?this.setView(this.getCenter(),i,{zoom:a}):(this._zoom=i,this)},zoomIn:function(i,a){return i=i||(et.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom+i,a)},zoomOut:function(i,a){return i=i||(et.any3d?this.options.zoomDelta:1),this.setZoom(this._zoom-i,a)},setZoomAround:function(i,a,s){var r=this.getZoomScale(a),c=this.getSize().divideBy(2),h=i instanceof P?i:this.latLngToContainerPoint(i),_=h.subtract(c).multiplyBy(1-1/r),T=this.containerPointToLatLng(c.add(_));return this.setView(T,a,{zoom:s})},_getBoundsCenterZoom:function(i,a){a=a||{},i=i.getBounds?i.getBounds():J(i);var s=m(a.paddingTopLeft||a.padding||[0,0]),r=m(a.paddingBottomRight||a.padding||[0,0]),c=this.getBoundsZoom(i,!1,s.add(r));if(c=typeof a.maxZoom=="number"?Math.min(a.maxZoom,c):c,c===1/0)return{center:i.getCenter(),zoom:c};var h=r.subtract(s).divideBy(2),_=this.project(i.getSouthWest(),c),T=this.project(i.getNorthEast(),c),N=this.unproject(_.add(T).divideBy(2).add(h),c);return{center:N,zoom:c}},fitBounds:function(i,a){if(i=J(i),!i.isValid())throw new Error("Bounds are not valid.");var s=this._getBoundsCenterZoom(i,a);return this.setView(s.center,s.zoom,a)},fitWorld:function(i){return this.fitBounds([[-90,-180],[90,180]],i)},panTo:function(i,a){return this.setView(i,this._zoom,{pan:a})},panBy:function(i,a){if(i=m(i).round(),a=a||{},!i.x&&!i.y)return this.fire("moveend");if(a.animate!==!0&&!this.getSize().contains(i))return this._resetView(this.unproject(this.project(this.getCenter()).add(i)),this.getZoom()),this;if(this._panAnim||(this._panAnim=new tl,this._panAnim.on({step:this._onPanTransitionStep,end:this._onPanTransitionEnd},this)),a.noMoveStart||this.fire("movestart"),a.animate!==!1){rt(this._mapPane,"leaflet-pan-anim");var s=this._getMapPanePos().subtract(i).round();this._panAnim.run(this._mapPane,s,a.duration||.25,a.easeLinearity)}else this._rawPanBy(i),this.fire("move").fire("moveend");return this},flyTo:function(i,a,s){if(s=s||{},s.animate===!1||!et.any3d)return this.setView(i,a,s);this._stop();var r=this.project(this.getCenter()),c=this.project(i),h=this.getSize(),_=this._zoom;i=X(i),a=a===void 0?_:a;var T=Math.max(h.x,h.y),N=T*this.getZoomScale(_,a),H=c.distanceTo(r)||1,K=1.42,W=K*K;function tt(Ht){var ci=Ht?-1:1,xi=Ht?N:T,Ri=N*N-T*T+ci*W*W*H*H,Si=2*xi*W*H,Za=Ri/Si,dl=Math.sqrt(Za*Za+1)-Za,ja=dl<1e-9?-18:Math.log(dl);return ja}function st(Ht){return(Math.exp(Ht)-Math.exp(-Ht))/2}function Bt(Ht){return(Math.exp(Ht)+Math.exp(-Ht))/2}function Yt(Ht){return st(Ht)/Bt(Ht)}var oe=tt(0);function ke(Ht){return T*(Bt(oe)/Bt(oe+K*Ht))}function To(Ht){return T*(Bt(oe)*Yt(oe+K*Ht)-st(oe))/W}function wo(Ht){return 1-Math.pow(1-Ht,1.5)}var Ba=Date.now(),Vn=(tt(1)-oe)/K,Mo=s.duration?1e3*s.duration:1e3*Vn*.8;function Xn(){var Ht=(Date.now()-Ba)/Mo,ci=wo(Ht)*Vn;Ht<=1?(this._flyToFrame=Ut(Xn,this),this._move(this.unproject(r.add(c.subtract(r).multiplyBy(To(ci)/H)),_),this.getScaleZoom(T/ke(ci),_),{flyTo:!0})):this._move(i,a)._moveEnd(!0)}return this._moveStart(!0,s.noMoveStart),Xn.call(this),this},flyToBounds:function(i,a){var s=this._getBoundsCenterZoom(i,a);return this.flyTo(s.center,s.zoom,a)},setMaxBounds:function(i){return i=J(i),this.listens("moveend",this._panInsideMaxBounds)&&this.off("moveend",this._panInsideMaxBounds),i.isValid()?(this.options.maxBounds=i,this._loaded&&this._panInsideMaxBounds(),this.on("moveend",this._panInsideMaxBounds)):(this.options.maxBounds=null,this)},setMinZoom:function(i){var a=this.options.minZoom;return this.options.minZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()<this.options.minZoom)?this.setZoom(i):this},setMaxZoom:function(i){var a=this.options.maxZoom;return this.options.maxZoom=i,this._loaded&&a!==i&&(this.fire("zoomlevelschange"),this.getZoom()>this.options.maxZoom)?this.setZoom(i):this},panInsideBounds:function(i,a){this._enforcingBounds=!0;var s=this.getCenter(),r=this._limitCenter(s,this._zoom,J(i));return s.equals(r)||this.panTo(r,a),this._enforcingBounds=!1,this},panInside:function(i,a){a=a||{};var s=m(a.paddingTopLeft||a.padding||[0,0]),r=m(a.paddingBottomRight||a.padding||[0,0]),c=this.project(this.getCenter()),h=this.project(i),_=this.getPixelBounds(),T=Y([_.min.add(s),_.max.subtract(r)]),N=T.getSize();if(!T.contains(h)){this._enforcingBounds=!0;var H=h.subtract(T.getCenter()),K=T.extend(h).getSize().subtract(N);c.x+=H.x<0?-K.x:K.x,c.y+=H.y<0?-K.y:K.y,this.panTo(this.unproject(c),a),this._enforcingBounds=!1}return this},invalidateSize:function(i){if(!this._loaded)return this;i=B({animate:!1,pan:!0},i===!0?{animate:!0}:i);var a=this.getSize();this._sizeChanged=!0,this._lastCenter=null;var s=this.getSize(),r=a.divideBy(2).round(),c=s.divideBy(2).round(),h=r.subtract(c);return!h.x&&!h.y?this:(i.animate&&i.pan?this.panBy(h):(i.pan&&this._rawPanBy(h),this.fire("move"),i.debounceMoveend?(clearTimeout(this._sizeTimer),this._sizeTimer=setTimeout(k(this.fire,this,"moveend"),200)):this.fire("moveend")),this.fire("resize",{oldSize:a,newSize:s}))},stop:function(){return this.setZoom(this._limitZoom(this._zoom)),this.options.zoomSnap||this.fire("viewreset"),this._stop()},locate:function(i){if(i=this._locateOptions=B({timeout:1e4,watch:!1},i),!("geolocation"in navigator))return this._handleGeolocationError({code:0,message:"Geolocation not supported."}),this;var a=k(this._handleGeolocationResponse,this),s=k(this._handleGeolocationError,this);return i.watch?this._locationWatchId=navigator.geolocation.watchPosition(a,s,i):navigator.geolocation.getCurrentPosition(a,s,i),this},stopLocate:function(){return navigator.geolocation&&navigator.geolocation.clearWatch&&navigator.geolocation.clearWatch(this._locationWatchId),this._locateOptions&&(this._locateOptions.setView=!1),this},_handleGeolocationError:function(i){if(this._container._leaflet_id){var a=i.code,s=i.message||(a===1?"permission denied":a===2?"position unavailable":"timeout");this._locateOptions.setView&&!this._loaded&&this.fitWorld(),this.fire("locationerror",{code:a,message:"Geolocation error: "+s+"."})}},_handleGeolocationResponse:function(i){if(this._container._leaflet_id){var a=i.coords.latitude,s=i.coords.longitude,r=new $(a,s),c=r.toBounds(i.coords.accuracy*2),h=this._locateOptions;if(h.setView){var _=this.getBoundsZoom(c);this.setView(r,h.maxZoom?Math.min(_,h.maxZoom):_)}var T={latlng:r,bounds:c,timestamp:i.timestamp};for(var N in i.coords)typeof i.coords[N]=="number"&&(T[N]=i.coords[N]);this.fire("locationfound",T)}},addHandler:function(i,a){if(!a)return this;var s=this[i]=new a(this);return this._handlers.push(s),this.options[i]&&s.enable(),this},remove:function(){if(this._initEvents(!0),this.options.maxBounds&&this.off("moveend",this._panInsideMaxBounds),this._containerId!==this._container._leaflet_id)throw new Error("Map container is being reused by another instance");try{delete this._container._leaflet_id,delete this._containerId}catch{this._container._leaflet_id=void 0,this._containerId=void 0}this._locationWatchId!==void 0&&this.stopLocate(),this._stop(),Dt(this._mapPane),this._clearControlPos&&this._clearControlPos(),this._resizeRequest&&(Et(this._resizeRequest),this._resizeRequest=null),this._clearHandlers(),this._loaded&&this.fire("unload");var i;for(i in this._layers)this._layers[i].remove();for(i in this._panes)Dt(this._panes[i]);return this._layers=[],this._panes=[],delete this._mapPane,delete this._renderer,this},createPane:function(i,a){var s="leaflet-pane"+(i?" leaflet-"+i.replace("Pane","")+"-pane":""),r=yt("div",s,a||this._mapPane);return i&&(this._panes[i]=r),r},getCenter:function(){return this._checkIfLoaded(),this._lastCenter&&!this._moved()?this._lastCenter.clone():this.layerPointToLatLng(this._getCenterLayerPoint())},getZoom:function(){return this._zoom},getBounds:function(){var i=this.getPixelBounds(),a=this.unproject(i.getBottomLeft()),s=this.unproject(i.getTopRight());return new G(a,s)},getMinZoom:function(){return this.options.minZoom===void 0?this._layersMinZoom||0:this.options.minZoom},getMaxZoom:function(){return this.options.maxZoom===void 0?this._layersMaxZoom===void 0?1/0:this._layersMaxZoom:this.options.maxZoom},getBoundsZoom:function(i,a,s){i=J(i),s=m(s||[0,0]);var r=this.getZoom()||0,c=this.getMinZoom(),h=this.getMaxZoom(),_=i.getNorthWest(),T=i.getSouthEast(),N=this.getSize().subtract(s),H=Y(this.project(T,r),this.project(_,r)).getSize(),K=et.any3d?this.options.zoomSnap:1,W=N.x/H.x,tt=N.y/H.y,st=a?Math.max(W,tt):Math.min(W,tt);return r=this.getScaleZoom(st,r),K&&(r=Math.round(r/(K/100))*(K/100),r=a?Math.ceil(r/K)*K:Math.floor(r/K)*K),Math.max(c,Math.min(h,r))},getSize:function(){return(!this._size||this._sizeChanged)&&(this._size=new P(this._container.clientWidth||0,this._container.clientHeight||0),this._sizeChanged=!1),this._size.clone()},getPixelBounds:function(i,a){var s=this._getTopLeftPoint(i,a);return new R(s,s.add(this.getSize()))},getPixelOrigin:function(){return this._checkIfLoaded(),this._pixelOrigin},getPixelWorldBounds:function(i){return this.options.crs.getProjectedBounds(i===void 0?this.getZoom():i)},getPane:function(i){return typeof i=="string"?this._panes[i]:i},getPanes:function(){return this._panes},getContainer:function(){return this._container},getZoomScale:function(i,a){var s=this.options.crs;return a=a===void 0?this._zoom:a,s.scale(i)/s.scale(a)},getScaleZoom:function(i,a){var s=this.options.crs;a=a===void 0?this._zoom:a;var r=s.zoom(i*s.scale(a));return isNaN(r)?1/0:r},project:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.latLngToPoint(X(i),a)},unproject:function(i,a){return a=a===void 0?this._zoom:a,this.options.crs.pointToLatLng(m(i),a)},layerPointToLatLng:function(i){var a=m(i).add(this.getPixelOrigin());return this.unproject(a)},latLngToLayerPoint:function(i){var a=this.project(X(i))._round();return a._subtract(this.getPixelOrigin())},wrapLatLng:function(i){return this.options.crs.wrapLatLng(X(i))},wrapLatLngBounds:function(i){return this.options.crs.wrapLatLngBounds(J(i))},distance:function(i,a){return this.options.crs.distance(X(i),X(a))},containerPointToLayerPoint:function(i){return m(i).subtract(this._getMapPanePos())},layerPointToContainerPoint:function(i){return m(i).add(this._getMapPanePos())},containerPointToLatLng:function(i){var a=this.containerPointToLayerPoint(m(i));return this.layerPointToLatLng(a)},latLngToContainerPoint:function(i){return this.layerPointToContainerPoint(this.latLngToLayerPoint(X(i)))},mouseEventToContainerPoint:function(i){return pe(i,this._container)},mouseEventToLayerPoint:function(i){return this.containerPointToLayerPoint(this.mouseEventToContainerPoint(i))},mouseEventToLatLng:function(i){return this.layerPointToLatLng(this.mouseEventToLayerPoint(i))},_initContainer:function(i){var a=this._container=Fa(i);if(a){if(a._leaflet_id)throw new Error("Map container is already initialized.")}else throw new Error("Map container not found.");ot(a,"scroll",this._onScroll,this),this._containerId=A(a)},_initLayout:function(){var i=this._container;this._fadeAnimated=this.options.fadeAnimation&&et.any3d,rt(i,"leaflet-container"+(et.touch?" leaflet-touch":"")+(et.retina?" leaflet-retina":"")+(et.ielt9?" leaflet-oldie":"")+(et.safari?" leaflet-safari":"")+(this._fadeAnimated?" leaflet-fade-anim":""));var a=Ki(i,"position");a!=="absolute"&&a!=="relative"&&a!=="fixed"&&a!=="sticky"&&(i.style.position="relative"),this._initPanes(),this._initControlPos&&this._initControlPos()},_initPanes:function(){var i=this._panes={};this._paneRenderers={},this._mapPane=this.createPane("mapPane",this._container),Gt(this._mapPane,new P(0,0)),this.createPane("tilePane"),this.createPane("overlayPane"),this.createPane("shadowPane"),this.createPane("markerPane"),this.createPane("tooltipPane"),this.createPane("popupPane"),this.options.markerZoomAnimation||(rt(i.markerPane,"leaflet-zoom-hide"),rt(i.shadowPane,"leaflet-zoom-hide"))},_resetView:function(i,a,s){Gt(this._mapPane,new P(0,0));var r=!this._loaded;this._loaded=!0,a=this._limitZoom(a),this.fire("viewprereset");var c=this._zoom!==a;this._moveStart(c,s)._move(i,a)._moveEnd(c),this.fire("viewreset"),r&&this.fire("load")},_moveStart:function(i,a){return i&&this.fire("zoomstart"),a||this.fire("movestart"),this},_move:function(i,a,s,r){a===void 0&&(a=this._zoom);var c=this._zoom!==a;return this._zoom=a,this._lastCenter=i,this._pixelOrigin=this._getNewPixelOrigin(i),r?s&&s.pinch&&this.fire("zoom",s):((c||s&&s.pinch)&&this.fire("zoom",s),this.fire("move",s)),this},_moveEnd:function(i){return i&&this.fire("zoomend"),this.fire("moveend")},_stop:function(){return Et(this._flyToFrame),this._panAnim&&this._panAnim.stop(),this},_rawPanBy:function(i){Gt(this._mapPane,this._getMapPanePos().subtract(i))},_getZoomSpan:function(){return this.getMaxZoom()-this.getMinZoom()},_panInsideMaxBounds:function(){this._enforcingBounds||this.panInsideBounds(this.options.maxBounds)},_checkIfLoaded:function(){if(!this._loaded)throw new Error("Set map center and zoom first.")},_initEvents:function(i){this._targets={},this._targets[A(this._container)]=this;var a=i?St:ot;a(this._container,"click dblclick mousedown mouseup mouseover mouseout mousemove contextmenu keypress keydown keyup",this._handleDOMEvent,this),this.options.trackResize&&a(window,"resize",this._onResize,this),et.any3d&&this.options.transform3DLimit&&(i?this.off:this.on).call(this,"moveend",this._onMoveEnd)},_onResize:function(){Et(this._resizeRequest),this._resizeRequest=Ut(function(){this.invalidateSize({debounceMoveend:!0})},this)},_onScroll:function(){this._container.scrollTop=0,this._container.scrollLeft=0},_onMoveEnd:function(){var i=this._getMapPanePos();Math.max(Math.abs(i.x),Math.abs(i.y))>=this.options.transform3DLimit&&this._resetView(this.getCenter(),this.getZoom())},_findEventTargets:function(i,a){for(var s=[],r,c=a==="mouseout"||a==="mouseover",h=i.target||i.srcElement,_=!1;h;){if(r=this._targets[A(h)],r&&(a==="click"||a==="preclick")&&this._draggableMoved(r)){_=!0;break}if(r&&r.listens(a,!0)&&(c&&!$i(h,i)||(s.push(r),c))||h===this._container)break;h=h.parentNode}return!s.length&&!_&&!c&&this.listens(a,!0)&&(s=[this]),s},_isClickDisabled:function(i){for(;i&&i!==this._container;){if(i._leaflet_disable_click)return!0;i=i.parentNode}},_handleDOMEvent:function(i){var a=i.target||i.srcElement;if(!(!this._loaded||a._leaflet_disable_events||i.type==="click"&&this._isClickDisabled(a))){var s=i.type;s==="mousedown"&&xa(a),this._fireDOMEvent(i,s)}},_mouseEvents:["click","dblclick","mouseover","mouseout","contextmenu"],_fireDOMEvent:function(i,a,s){if(i.type==="click"){var r=B({},i);r.type="preclick",this._fireDOMEvent(r,r.type,s)}var c=this._findEventTargets(i,a);if(s){for(var h=[],_=0;_<s.length;_++)s[_].listens(a,!0)&&h.push(s[_]);c=h.concat(c)}if(c.length){a==="contextmenu"&&Qt(i);var T=c[0],N={originalEvent:i};if(i.type!=="keypress"&&i.type!=="keydown"&&i.type!=="keyup"){var H=T.getLatLng&&(!T._radius||T._radius<=10);N.containerPoint=H?this.latLngToContainerPoint(T.getLatLng()):this.mouseEventToContainerPoint(i),N.layerPoint=this.containerPointToLayerPoint(N.containerPoint),N.latlng=H?T.getLatLng():this.layerPointToLatLng(N.layerPoint)}for(_=0;_<c.length;_++)if(c[_].fire(a,N,!0),N.originalEvent._stopped||c[_].options.bubblingMouseEvents===!1&&fe(this._mouseEvents,a)!==-1)return}},_draggableMoved:function(i){return i=i.dragging&&i.dragging.enabled()?i:this,i.dragging&&i.dragging.moved()||this.boxZoom&&this.boxZoom.moved()},_clearHandlers:function(){for(var i=0,a=this._handlers.length;i<a;i++)this._handlers[i].disable()},whenReady:function(i,a){return this._loaded?i.call(a||this,{target:this}):this.on("load",i,a),this},_getMapPanePos:function(){return Li(this._mapPane)||new P(0,0)},_moved:function(){var i=this._getMapPanePos();return i&&!i.equals([0,0])},_getTopLeftPoint:function(i,a){var s=i&&a!==void 0?this._getNewPixelOrigin(i,a):this.getPixelOrigin();return s.subtract(this._getMapPanePos())},_getNewPixelOrigin:function(i,a){var s=this.getSize()._divideBy(2);return this.project(i,a)._subtract(s)._add(this._getMapPanePos())._round()},_latLngToNewLayerPoint:function(i,a,s){var r=this._getNewPixelOrigin(s,a);return this.project(i,a)._subtract(r)},_latLngBoundsToNewLayerBounds:function(i,a,s){var r=this._getNewPixelOrigin(s,a);return Y([this.project(i.getSouthWest(),a)._subtract(r),this.project(i.getNorthWest(),a)._subtract(r),this.project(i.getSouthEast(),a)._subtract(r),this.project(i.getNorthEast(),a)._subtract(r)])},_getCenterLayerPoint:function(){return this.containerPointToLayerPoint(this.getSize()._divideBy(2))},_getCenterOffset:function(i){return this.latLngToLayerPoint(i).subtract(this._getCenterLayerPoint())},_limitCenter:function(i,a,s){if(!s)return i;var r=this.project(i,a),c=this.getSize().divideBy(2),h=new R(r.subtract(c),r.add(c)),_=this._getBoundsOffset(h,s,a);return Math.abs(_.x)<=1&&Math.abs(_.y)<=1?i:this.unproject(r.add(_),a)},_limitOffset:function(i,a){if(!a)return i;var s=this.getPixelBounds(),r=new R(s.min.add(i),s.max.add(i));return i.add(this._getBoundsOffset(r,a))},_getBoundsOffset:function(i,a,s){var r=Y(this.project(a.getNorthEast(),s),this.project(a.getSouthWest(),s)),c=r.min.subtract(i.min),h=r.max.subtract(i.max),_=this._rebound(c.x,-h.x),T=this._rebound(c.y,-h.y);return new P(_,T)},_rebound:function(i,a){return i+a>0?Math.round(i-a)/2:Math.max(0,Math.ceil(i))-Math.max(0,Math.floor(a))},_limitZoom:function(i){var a=this.getMinZoom(),s=this.getMaxZoom(),r=et.any3d?this.options.zoomSnap:1;return r&&(i=Math.round(i/r)*r),Math.max(a,Math.min(s,i))},_onPanTransitionStep:function(){this.fire("move")},_onPanTransitionEnd:function(){Rt(this._mapPane,"leaflet-pan-anim"),this.fire("moveend")},_tryAnimatedPan:function(i,a){var s=this._getCenterOffset(i)._trunc();return(a&&a.animate)!==!0&&!this.getSize().contains(s)?!1:(this.panBy(s,a),!0)},_createAnimProxy:function(){var i=this._proxy=yt("div","leaflet-proxy leaflet-zoom-animated");this._panes.mapPane.appendChild(i),this.on("zoomanim",function(a){var s=ga,r=this._proxy.style[s];qe(this._proxy,this.project(a.center,a.zoom),this.getZoomScale(a.zoom,1)),r===this._proxy.style[s]&&this._animatingZoom&&this._onZoomTransitionEnd()},this),this.on("load moveend",this._animMoveEnd,this),this._on("unload",this._destroyAnimProxy,this)},_destroyAnimProxy:function(){Dt(this._proxy),this.off("load moveend",this._animMoveEnd,this),delete this._proxy},_animMoveEnd:function(){var i=this.getCenter(),a=this.getZoom();qe(this._proxy,this.project(i,a),this.getZoomScale(a,1))},_catchTransitionEnd:function(i){this._animatingZoom&&i.propertyName.indexOf("transform")>=0&&this._onZoomTransitionEnd()},_nothingToAnimate:function(){return!this._container.getElementsByClassName("leaflet-zoom-animated").length},_tryAnimatedZoom:function(i,a,s){if(this._animatingZoom)return!0;if(s=s||{},!this._zoomAnimated||s.animate===!1||this._nothingToAnimate()||Math.abs(a-this._zoom)>this.options.zoomAnimationThreshold)return!1;var r=this.getZoomScale(a),c=this._getCenterOffset(i)._divideBy(1-1/r);return s.animate!==!0&&!this.getSize().contains(c)?!1:(Ut(function(){this._moveStart(!0,s.noMoveStart||!1)._animateZoom(i,a,!0)},this),!0)},_animateZoom:function(i,a,s,r){this._mapPane&&(s&&(this._animatingZoom=!0,this._animateToCenter=i,this._animateToZoom=a,rt(this._mapPane,"leaflet-zoom-anim")),this.fire("zoomanim",{center:i,zoom:a,noUpdate:r}),this._tempFireZoomEvent||(this._tempFireZoomEvent=this._zoom!==this._animateToZoom),this._move(this._animateToCenter,this._animateToZoom,void 0,!0),setTimeout(k(this._onZoomTransitionEnd,this),250))},_onZoomTransitionEnd:function(){this._animatingZoom&&(this._mapPane&&Rt(this._mapPane,"leaflet-zoom-anim"),this._animatingZoom=!1,this._move(this._animateToCenter,this._animateToZoom,void 0,!0),this._tempFireZoomEvent&&this.fire("zoom"),delete this._tempFireZoomEvent,this.fire("move"),this._moveEnd(!0))}});function wa(i,a){return new _t(i,a)}var Me=de.extend({options:{position:"topright"},initialize:function(i){mt(this,i)},getPosition:function(){return this.options.position},setPosition:function(i){var a=this._map;return a&&a.removeControl(this),this.options.position=i,a&&a.addControl(this),this},getContainer:function(){return this._container},addTo:function(i){this.remove(),this._map=i;var a=this._container=this.onAdd(i),s=this.getPosition(),r=i._controlCorners[s];return rt(a,"leaflet-control"),s.indexOf("bottom")!==-1?r.insertBefore(a,r.firstChild):r.appendChild(a),this._map.on("unload",this.remove,this),this},remove:function(){return this._map?(Dt(this._container),this.onRemove&&this.onRemove(this._map),this._map.off("unload",this.remove,this),this._map=null,this):this},_refocusOnMap:function(i){this._map&&i&&i.screenX>0&&i.screenY>0&&this._map.getContainer().focus()}}),Rn=function(i){return new Me(i)};_t.include({addControl:function(i){return i.addTo(this),this},removeControl:function(i){return i.remove(),this},_initControlPos:function(){var i=this._controlCorners={},a="leaflet-",s=this._controlContainer=yt("div",a+"control-container",this._container);function r(c,h){var _=a+c+" "+a+h;i[c+h]=yt("div",_,s)}r("top","left"),r("top","right"),r("bottom","left"),r("bottom","right")},_clearControlPos:function(){for(var i in this._controlCorners)Dt(this._controlCorners[i]);Dt(this._controlContainer),delete this._controlCorners,delete this._controlContainer}});var ao=Me.extend({options:{collapsed:!0,position:"topright",autoZIndex:!0,hideSingleBase:!1,sortLayers:!1,sortFunction:function(i,a,s,r){return s<r?-1:r<s?1:0}},initialize:function(i,a,s){mt(this,s),this._layerControlInputs=[],this._layers=[],this._lastZIndex=0,this._handlingClick=!1,this._preventClick=!1;for(var r in i)this._addLayer(i[r],r);for(r in a)this._addLayer(a[r],r,!0)},onAdd:function(i){this._initLayout(),this._update(),this._map=i,i.on("zoomend",this._checkDisabledLayers,this);for(var a=0;a<this._layers.length;a++)this._layers[a].layer.on("add remove",this._onLayerChange,this);return this._container},addTo:function(i){return Me.prototype.addTo.call(this,i),this._expandIfNotCollapsed()},onRemove:function(){this._map.off("zoomend",this._checkDisabledLayers,this);for(var i=0;i<this._layers.length;i++)this._layers[i].layer.off("add remove",this._onLayerChange,this)},addBaseLayer:function(i,a){return this._addLayer(i,a),this._map?this._update():this},addOverlay:function(i,a){return this._addLayer(i,a,!0),this._map?this._update():this},removeLayer:function(i){i.off("add remove",this._onLayerChange,this);var a=this._getLayer(A(i));return a&&this._layers.splice(this._layers.indexOf(a),1),this._map?this._update():this},expand:function(){rt(this._container,"leaflet-control-layers-expanded"),this._section.style.height=null;var i=this._map.getSize().y-(this._container.offsetTop+50);return i<this._section.clientHeight?(rt(this._section,"leaflet-control-layers-scrollbar"),this._section.style.height=i+"px"):Rt(this._section,"leaflet-control-layers-scrollbar"),this._checkDisabledLayers(),this},collapse:function(){return Rt(this._container,"leaflet-control-layers-expanded"),this},_initLayout:function(){var i="leaflet-control-layers",a=this._container=yt("div",i),s=this.options.collapsed;a.setAttribute("aria-haspopup",!0),Dn(a),Cn(a);var r=this._section=yt("section",i+"-list");s&&(this._map.on("click",this.collapse,this),ot(a,{mouseenter:this._expandSafely,mouseleave:this.collapse},this));var c=this._layersLink=yt("a",i+"-toggle",a);c.href="#",c.title="Layers",c.setAttribute("role","button"),ot(c,{keydown:function(h){h.keyCode===13&&this._expandSafely()},click:function(h){Qt(h),this._expandSafely()}},this),s||this.expand(),this._baseLayersList=yt("div",i+"-base",r),this._separator=yt("div",i+"-separator",r),this._overlaysList=yt("div",i+"-overlays",r),a.appendChild(r)},_getLayer:function(i){for(var a=0;a<this._layers.length;a++)if(this._layers[a]&&A(this._layers[a].layer)===i)return this._layers[a]},_addLayer:function(i,a,s){this._map&&i.on("add remove",this._onLayerChange,this),this._layers.push({layer:i,name:a,overlay:s}),this.options.sortLayers&&this._layers.sort(k(function(r,c){return this.options.sortFunction(r.layer,c.layer,r.name,c.name)},this)),this.options.autoZIndex&&i.setZIndex&&(this._lastZIndex++,i.setZIndex(this._lastZIndex)),this._expandIfNotCollapsed()},_update:function(){if(!this._container)return this;me(this._baseLayersList),me(this._overlaysList),this._layerControlInputs=[];var i,a,s,r,c=0;for(s=0;s<this._layers.length;s++)r=this._layers[s],this._addItem(r),a=a||r.overlay,i=i||!r.overlay,c+=r.overlay?0:1;return this.options.hideSingleBase&&(i=i&&c>1,this._baseLayersList.style.display=i?"":"none"),this._separator.style.display=a&&i?"":"none",this},_onLayerChange:function(i){this._handlingClick||this._update();var a=this._getLayer(A(i.target)),s=a.overlay?i.type==="add"?"overlayadd":"overlayremove":i.type==="add"?"baselayerchange":null;s&&this._map.fire(s,a)},_createRadioElement:function(i,a){var s='<input type="radio" class="leaflet-control-layers-selector" name="'+i+'"'+(a?' checked="checked"':"")+"/>",r=document.createElement("div");return r.innerHTML=s,r.firstChild},_addItem:function(i){var a=document.createElement("label"),s=this._map.hasLayer(i.layer),r;i.overlay?(r=document.createElement("input"),r.type="checkbox",r.className="leaflet-control-layers-selector",r.defaultChecked=s):r=this._createRadioElement("leaflet-base-layers_"+A(this),s),this._layerControlInputs.push(r),r.layerId=A(i.layer),ot(r,"click",this._onInputClick,this);var c=document.createElement("span");c.innerHTML=" "+i.name;var h=document.createElement("span");a.appendChild(h),h.appendChild(r),h.appendChild(c);var _=i.overlay?this._overlaysList:this._baseLayersList;return _.appendChild(a),this._checkDisabledLayers(),a},_onInputClick:function(){if(!this._preventClick){var i=this._layerControlInputs,a,s,r=[],c=[];this._handlingClick=!0;for(var h=i.length-1;h>=0;h--)a=i[h],s=this._getLayer(a.layerId).layer,a.checked?r.push(s):a.checked||c.push(s);for(h=0;h<c.length;h++)this._map.hasLayer(c[h])&&this._map.removeLayer(c[h]);for(h=0;h<r.length;h++)this._map.hasLayer(r[h])||this._map.addLayer(r[h]);this._handlingClick=!1,this._refocusOnMap()}},_checkDisabledLayers:function(){for(var i=this._layerControlInputs,a,s,r=this._map.getZoom(),c=i.length-1;c>=0;c--)a=i[c],s=this._getLayer(a.layerId).layer,a.disabled=s.options.minZoom!==void 0&&r<s.options.minZoom||s.options.maxZoom!==void 0&&r>s.options.maxZoom},_expandIfNotCollapsed:function(){return this._map&&!this.options.collapsed&&this.expand(),this},_expandSafely:function(){var i=this._section;this._preventClick=!0,ot(i,"click",Qt),this.expand();var a=this;setTimeout(function(){St(i,"click",Qt),a._preventClick=!1})}}),zu=function(i,a,s){return new ao(i,a,s)},Xl=Me.extend({options:{position:"topleft",zoomInText:'<span aria-hidden="true">+</span>',zoomInTitle:"Zoom in",zoomOutText:'<span aria-hidden="true">&#x2212;</span>',zoomOutTitle:"Zoom out"},onAdd:function(i){var a="leaflet-control-zoom",s=yt("div",a+" leaflet-bar"),r=this.options;return this._zoomInButton=this._createButton(r.zoomInText,r.zoomInTitle,a+"-in",s,this._zoomIn),this._zoomOutButton=this._createButton(r.zoomOutText,r.zoomOutTitle,a+"-out",s,this._zoomOut),this._updateDisabled(),i.on("zoomend zoomlevelschange",this._updateDisabled,this),s},onRemove:function(i){i.off("zoomend zoomlevelschange",this._updateDisabled,this)},disable:function(){return this._disabled=!0,this._updateDisabled(),this},enable:function(){return this._disabled=!1,this._updateDisabled(),this},_zoomIn:function(i){!this._disabled&&this._map._zoom<this._map.getMaxZoom()&&this._map.zoomIn(this._map.options.zoomDelta*(i.shiftKey?3:1))},_zoomOut:function(i){!this._disabled&&this._map._zoom>this._map.getMinZoom()&&this._map.zoomOut(this._map.options.zoomDelta*(i.shiftKey?3:1))},_createButton:function(i,a,s,r,c){var h=yt("a",s,r);return h.innerHTML=i,h.href="#",h.title=a,h.setAttribute("role","button"),h.setAttribute("aria-label",a),Dn(h),ot(h,"click",oi),ot(h,"click",c,this),ot(h,"click",this._refocusOnMap,this),h},_updateDisabled:function(){var i=this._map,a="leaflet-disabled";Rt(this._zoomInButton,a),Rt(this._zoomOutButton,a),this._zoomInButton.setAttribute("aria-disabled","false"),this._zoomOutButton.setAttribute("aria-disabled","false"),(this._disabled||i._zoom===i.getMinZoom())&&(rt(this._zoomOutButton,a),this._zoomOutButton.setAttribute("aria-disabled","true")),(this._disabled||i._zoom===i.getMaxZoom())&&(rt(this._zoomInButton,a),this._zoomInButton.setAttribute("aria-disabled","true"))}});_t.mergeOptions({zoomControl:!0}),_t.addInitHook(function(){this.options.zoomControl&&(this.zoomControl=new Xl,this.addControl(this.zoomControl))});var Ql=function(i){return new Xl(i)},lo=Me.extend({options:{position:"bottomleft",maxWidth:100,metric:!0,imperial:!0},onAdd:function(i){var a="leaflet-control-scale",s=yt("div",a),r=this.options;return this._addScales(r,a+"-line",s),i.on(r.updateWhenIdle?"moveend":"move",this._update,this),i.whenReady(this._update,this),s},onRemove:function(i){i.off(this.options.updateWhenIdle?"moveend":"move",this._update,this)},_addScales:function(i,a,s){i.metric&&(this._mScale=yt("div",a,s)),i.imperial&&(this._iScale=yt("div",a,s))},_update:function(){var i=this._map,a=i.getSize().y/2,s=i.distance(i.containerPointToLatLng([0,a]),i.containerPointToLatLng([this.options.maxWidth,a]));this._updateScales(s)},_updateScales:function(i){this.options.metric&&i&&this._updateMetric(i),this.options.imperial&&i&&this._updateImperial(i)},_updateMetric:function(i){var a=this._getRoundNum(i),s=a<1e3?a+" m":a/1e3+" km";this._updateScale(this._mScale,s,a/i)},_updateImperial:function(i){var a=i*3.2808399,s,r,c;a>5280?(s=a/5280,r=this._getRoundNum(s),this._updateScale(this._iScale,r+" mi",r/s)):(c=this._getRoundNum(a),this._updateScale(this._iScale,c+" ft",c/a))},_updateScale:function(i,a,s){i.style.width=Math.round(this.options.maxWidth*s)+"px",i.innerHTML=a},_getRoundNum:function(i){var a=Math.pow(10,(Math.floor(i)+"").length-1),s=i/a;return s=s>=10?10:s>=5?5:s>=3?3:s>=2?2:1,a*s}}),Lu=function(i){return new lo(i)},Au='<svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8" class="leaflet-attribution-flag"><path fill="#4C7BE1" d="M0 0h12v4H0z"/><path fill="#FFD500" d="M0 4h12v3H0z"/><path fill="#E0BC00" d="M0 7h12v1H0z"/></svg>',Kl=Me.extend({options:{position:"bottomright",prefix:'<a href="https://leafletjs.com" title="A JavaScript library for interactive maps">'+(et.inlineSvg?Au+" ":"")+"Leaflet</a>"},initialize:function(i){mt(this,i),this._attributions={}},onAdd:function(i){i.attributionControl=this,this._container=yt("div","leaflet-control-attribution"),Dn(this._container);for(var a in i._layers)i._layers[a].getAttribution&&this.addAttribution(i._layers[a].getAttribution());return this._update(),i.on("layeradd",this._addAttribution,this),this._container},onRemove:function(i){i.off("layeradd",this._addAttribution,this)},_addAttribution:function(i){i.layer.getAttribution&&(this.addAttribution(i.layer.getAttribution()),i.layer.once("remove",function(){this.removeAttribution(i.layer.getAttribution())},this))},setPrefix:function(i){return this.options.prefix=i,this._update(),this},addAttribution:function(i){return i?(this._attributions[i]||(this._attributions[i]=0),this._attributions[i]++,this._update(),this):this},removeAttribution:function(i){return i?(this._attributions[i]&&(this._attributions[i]--,this._update()),this):this},_update:function(){if(this._map){var i=[];for(var a in this._attributions)this._attributions[a]&&i.push(a);var s=[];this.options.prefix&&s.push(this.options.prefix),i.length&&s.push(i.join(", ")),this._container.innerHTML=s.join(' <span aria-hidden="true">|</span> ')}}});_t.mergeOptions({attributionControl:!0}),_t.addInitHook(function(){this.options.attributionControl&&new Kl().addTo(this)});var Ou=function(i){return new Kl(i)};Me.Layers=ao,Me.Zoom=Xl,Me.Scale=lo,Me.Attribution=Kl,Rn.layers=zu,Rn.zoom=Ql,Rn.scale=Lu,Rn.attribution=Ou;var Xe=de.extend({initialize:function(i){this._map=i},enable:function(){return this._enabled?this:(this._enabled=!0,this.addHooks(),this)},disable:function(){return this._enabled?(this._enabled=!1,this.removeHooks(),this):this},enabled:function(){return!!this._enabled}});Xe.addTo=function(i,a){return i.addHandler(a,this),this};var Nu={Events:D},so=et.touch?"touchstart mousedown":"mousedown",Oi=Q.extend({options:{clickTolerance:3},initialize:function(i,a,s,r){mt(this,r),this._element=i,this._dragStartTarget=a||i,this._preventOutline=s},enable:function(){this._enabled||(ot(this._dragStartTarget,so,this._onDown,this),this._enabled=!0)},disable:function(){this._enabled&&(Oi._dragging===this&&this.finishDrag(!0),St(this._dragStartTarget,so,this._onDown,this),this._enabled=!1,this._moved=!1)},_onDown:function(i){if(this._enabled&&(this._moved=!1,!zn(this._element,"leaflet-zoom-anim"))){if(i.touches&&i.touches.length!==1){Oi._dragging===this&&this.finishDrag();return}if(!(Oi._dragging||i.shiftKey||i.which!==1&&i.button!==1&&!i.touches)&&(Oi._dragging=this,this._preventOutline&&xa(this._element),ba(),ai(),!this._moving)){this.fire("down");var a=i.touches?i.touches[0]:i,s=Wi(this._element);this._startPoint=new P(a.clientX,a.clientY),this._startPos=Li(this._element),this._parentScale=Ai(s);var r=i.type==="mousedown";ot(document,r?"mousemove":"touchmove",this._onMove,this),ot(document,r?"mouseup":"touchend touchcancel",this._onUp,this)}}},_onMove:function(i){if(this._enabled){if(i.touches&&i.touches.length>1){this._moved=!0;return}var a=i.touches&&i.touches.length===1?i.touches[0]:i,s=new P(a.clientX,a.clientY)._subtract(this._startPoint);!s.x&&!s.y||Math.abs(s.x)+Math.abs(s.y)<this.options.clickTolerance||(s.x/=this._parentScale.x,s.y/=this._parentScale.y,Qt(i),this._moved||(this.fire("dragstart"),this._moved=!0,rt(document.body,"leaflet-dragging"),this._lastTarget=i.target||i.srcElement,window.SVGElementInstance&&this._lastTarget instanceof window.SVGElementInstance&&(this._lastTarget=this._lastTarget.correspondingUseElement),rt(this._lastTarget,"leaflet-drag-target")),this._newPos=this._startPos.add(s),this._moving=!0,this._lastEvent=i,this._updatePosition())}},_updatePosition:function(){var i={originalEvent:this._lastEvent};this.fire("predrag",i),Gt(this._element,this._newPos),this.fire("drag",i)},_onUp:function(){this._enabled&&this.finishDrag()},finishDrag:function(i){Rt(document.body,"leaflet-dragging"),this._lastTarget&&(Rt(this._lastTarget,"leaflet-drag-target"),this._lastTarget=null),St(document,"mousemove touchmove",this._onMove,this),St(document,"mouseup touchend touchcancel",this._onUp,this),Yl(),ya();var a=this._moved&&this._moving;this._moving=!1,Oi._dragging=!1,a&&this.fire("dragend",{noInertia:i,distance:this._newPos.distanceTo(this._startPos)})}});function oo(i,a,s){var r,c=[1,4,2,8],h,_,T,N,H,K,W,tt;for(h=0,K=i.length;h<K;h++)i[h]._code=tn(i[h],a);for(T=0;T<4;T++){for(W=c[T],r=[],h=0,K=i.length,_=K-1;h<K;_=h++)N=i[h],H=i[_],N._code&W?H._code&W||(tt=il(H,N,W,a,s),tt._code=tn(tt,a),r.push(tt)):(H._code&W&&(tt=il(H,N,W,a,s),tt._code=tn(tt,a),r.push(tt)),r.push(N));i=r}return i}function el(i,a){var s,r,c,h,_,T,N,H,K;if(!i||i.length===0)throw new Error("latlngs not passed");se(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var W=X([0,0]),tt=J(i),st=tt.getNorthWest().distanceTo(tt.getSouthWest())*tt.getNorthEast().distanceTo(tt.getNorthWest());st<1700&&(W=Jl(i));var Bt=i.length,Yt=[];for(s=0;s<Bt;s++){var oe=X(i[s]);Yt.push(a.project(X([oe.lat-W.lat,oe.lng-W.lng])))}for(T=N=H=0,s=0,r=Bt-1;s<Bt;r=s++)c=Yt[s],h=Yt[r],_=c.y*h.x-h.y*c.x,N+=(c.x+h.x)*_,H+=(c.y+h.y)*_,T+=_*3;T===0?K=Yt[0]:K=[N/T,H/T];var ke=a.unproject(m(K));return X([ke.lat+W.lat,ke.lng+W.lng])}function Jl(i){for(var a=0,s=0,r=0,c=0;c<i.length;c++){var h=X(i[c]);a+=h.lat,s+=h.lng,r++}return X([a/r,s/r])}var Cu={__proto__:null,clipPolygon:oo,polygonCenter:el,centroid:Jl};function uo(i,a){if(!a||!i.length)return i.slice();var s=a*a;return i=Bu(i,s),i=Ru(i,s),i}function Wl(i,a,s){return Math.sqrt(en(i,a,s,!0))}function Du(i,a,s){return en(i,a,s)}function Ru(i,a){var s=i.length,r=typeof Uint8Array<"u"?Uint8Array:Array,c=new r(s);c[0]=c[s-1]=1,Fl(i,c,a,0,s-1);var h,_=[];for(h=0;h<s;h++)c[h]&&_.push(i[h]);return _}function Fl(i,a,s,r,c){var h=0,_,T,N;for(T=r+1;T<=c-1;T++)N=en(i[T],i[r],i[c],!0),N>h&&(_=T,h=N);h>s&&(a[_]=1,Fl(i,a,s,r,_),Fl(i,a,s,_,c))}function Bu(i,a){for(var s=[i[0]],r=1,c=0,h=i.length;r<h;r++)Zu(i[r],i[c])>a&&(s.push(i[r]),c=r);return c<h-1&&s.push(i[h-1]),s}var ro;function co(i,a,s,r,c){var h=r?ro:tn(i,s),_=tn(a,s),T,N,H;for(ro=_;;){if(!(h|_))return[i,a];if(h&_)return!1;T=h||_,N=il(i,a,T,s,c),H=tn(N,s),T===h?(i=N,h=H):(a=N,_=H)}}function il(i,a,s,r,c){var h=a.x-i.x,_=a.y-i.y,T=r.min,N=r.max,H,K;return s&8?(H=i.x+h*(N.y-i.y)/_,K=N.y):s&4?(H=i.x+h*(T.y-i.y)/_,K=T.y):s&2?(H=N.x,K=i.y+_*(N.x-i.x)/h):s&1&&(H=T.x,K=i.y+_*(T.x-i.x)/h),new P(H,K,c)}function tn(i,a){var s=0;return i.x<a.min.x?s|=1:i.x>a.max.x&&(s|=2),i.y<a.min.y?s|=4:i.y>a.max.y&&(s|=8),s}function Zu(i,a){var s=a.x-i.x,r=a.y-i.y;return s*s+r*r}function en(i,a,s,r){var c=a.x,h=a.y,_=s.x-c,T=s.y-h,N=_*_+T*T,H;return N>0&&(H=((i.x-c)*_+(i.y-h)*T)/N,H>1?(c=s.x,h=s.y):H>0&&(c+=_*H,h+=T*H)),_=i.x-c,T=i.y-h,r?_*_+T*T:new P(c,h)}function se(i){return!wt(i[0])||typeof i[0][0]!="object"&&typeof i[0][0]<"u"}function fo(i){return console.warn("Deprecated use of _flat, please use L.LineUtil.isFlat instead."),se(i)}function Il(i,a){var s,r,c,h,_,T,N,H;if(!i||i.length===0)throw new Error("latlngs not passed");se(i)||(console.warn("latlngs are not flat! Only the first ring will be used"),i=i[0]);var K=X([0,0]),W=J(i),tt=W.getNorthWest().distanceTo(W.getSouthWest())*W.getNorthEast().distanceTo(W.getNorthWest());tt<1700&&(K=Jl(i));var st=i.length,Bt=[];for(s=0;s<st;s++){var Yt=X(i[s]);Bt.push(a.project(X([Yt.lat-K.lat,Yt.lng-K.lng])))}for(s=0,r=0;s<st-1;s++)r+=Bt[s].distanceTo(Bt[s+1])/2;if(r===0)H=Bt[0];else for(s=0,h=0;s<st-1;s++)if(_=Bt[s],T=Bt[s+1],c=_.distanceTo(T),h+=c,h>r){N=(h-r)/c,H=[T.x-N*(T.x-_.x),T.y-N*(T.y-_.y)];break}var oe=a.unproject(m(H));return X([oe.lat+K.lat,oe.lng+K.lng])}var ho={__proto__:null,simplify:uo,pointToSegmentDistance:Wl,closestPointOnSegment:Du,clipSegment:co,_getEdgeIntersection:il,_getBitCode:tn,_sqClosestPointOnSegment:en,isFlat:se,_flat:fo,polylineCenter:Il},nl={project:function(i){return new P(i.lng,i.lat)},unproject:function(i){return new $(i.y,i.x)},bounds:new R([-180,-90],[180,90])},al={R:6378137,R_MINOR:6356752314245179e-9,bounds:new R([-2003750834279e-5,-1549657073972e-5],[2003750834279e-5,1876465623138e-5]),project:function(i){var a=Math.PI/180,s=this.R,r=i.lat*a,c=this.R_MINOR/s,h=Math.sqrt(1-c*c),_=h*Math.sin(r),T=Math.tan(Math.PI/4-r/2)/Math.pow((1-_)/(1+_),h/2);return r=-s*Math.log(Math.max(T,1e-10)),new P(i.lng*a*s,r)},unproject:function(i){for(var a=180/Math.PI,s=this.R,r=this.R_MINOR/s,c=Math.sqrt(1-r*r),h=Math.exp(-i.y/s),_=Math.PI/2-2*Math.atan(h),T=0,N=.1,H;T<15&&Math.abs(N)>1e-7;T++)H=c*Math.sin(_),H=Math.pow((1-H)/(1+H),c/2),N=Math.PI/2-2*Math.atan(h*H)-_,_+=N;return new $(_*a,i.x*a/s)}},mo={__proto__:null,LonLat:nl,Mercator:al,SphericalMercator:ma},Bn=B({},xt,{code:"EPSG:3395",projection:al,transformation:function(){var i=.5/(Math.PI*al.R);return ki(i,.5,-i,.5)}()}),_o=B({},xt,{code:"EPSG:4326",projection:nl,transformation:ki(1/180,1,-1/180,.5)}),ju=B({},Pt,{projection:nl,transformation:ki(1,0,-1,0),scale:function(i){return Math.pow(2,i)},zoom:function(i){return Math.log(i)/Math.LN2},distance:function(i,a){var s=a.lng-i.lng,r=a.lat-i.lat;return Math.sqrt(s*s+r*r)},infinite:!0});Pt.Earth=xt,Pt.EPSG3395=Bn,Pt.EPSG3857=_a,Pt.EPSG900913=Bl,Pt.EPSG4326=_o,Pt.Simple=ju;var Qe=Q.extend({options:{pane:"overlayPane",attribution:null,bubblingMouseEvents:!0},addTo:function(i){return i.addLayer(this),this},remove:function(){return this.removeFrom(this._map||this._mapToAdd)},removeFrom:function(i){return i&&i.removeLayer(this),this},getPane:function(i){return this._map.getPane(i?this.options[i]||i:this.options.pane)},addInteractiveTarget:function(i){return this._map._targets[A(i)]=this,this},removeInteractiveTarget:function(i){return delete this._map._targets[A(i)],this},getAttribution:function(){return this.options.attribution},_layerAdd:function(i){var a=i.target;if(a.hasLayer(this)){if(this._map=a,this._zoomAnimated=a._zoomAnimated,this.getEvents){var s=this.getEvents();a.on(s,this),this.once("remove",function(){a.off(s,this)},this)}this.onAdd(a),this.fire("add"),a.fire("layeradd",{layer:this})}}});_t.include({addLayer:function(i){if(!i._layerAdd)throw new Error("The provided object is not a Layer.");var a=A(i);return this._layers[a]?this:(this._layers[a]=i,i._mapToAdd=this,i.beforeAdd&&i.beforeAdd(this),this.whenReady(i._layerAdd,i),this)},removeLayer:function(i){var a=A(i);return this._layers[a]?(this._loaded&&i.onRemove(this),delete this._layers[a],this._loaded&&(this.fire("layerremove",{layer:i}),i.fire("remove")),i._map=i._mapToAdd=null,this):this},hasLayer:function(i){return A(i)in this._layers},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},_addLayers:function(i){i=i?wt(i)?i:[i]:[];for(var a=0,s=i.length;a<s;a++)this.addLayer(i[a])},_addZoomLimit:function(i){(!isNaN(i.options.maxZoom)||!isNaN(i.options.minZoom))&&(this._zoomBoundLayers[A(i)]=i,this._updateZoomLevels())},_removeZoomLimit:function(i){var a=A(i);this._zoomBoundLayers[a]&&(delete this._zoomBoundLayers[a],this._updateZoomLevels())},_updateZoomLevels:function(){var i=1/0,a=-1/0,s=this._getZoomSpan();for(var r in this._zoomBoundLayers){var c=this._zoomBoundLayers[r].options;i=c.minZoom===void 0?i:Math.min(i,c.minZoom),a=c.maxZoom===void 0?a:Math.max(a,c.maxZoom)}this._layersMaxZoom=a===-1/0?void 0:a,this._layersMinZoom=i===1/0?void 0:i,s!==this._getZoomSpan()&&this.fire("zoomlevelschange"),this.options.maxZoom===void 0&&this._layersMaxZoom&&this.getZoom()>this._layersMaxZoom&&this.setZoom(this._layersMaxZoom),this.options.minZoom===void 0&&this._layersMinZoom&&this.getZoom()<this._layersMinZoom&&this.setZoom(this._layersMinZoom)}});var nn=Qe.extend({initialize:function(i,a){mt(this,a),this._layers={};var s,r;if(i)for(s=0,r=i.length;s<r;s++)this.addLayer(i[s])},addLayer:function(i){var a=this.getLayerId(i);return this._layers[a]=i,this._map&&this._map.addLayer(i),this},removeLayer:function(i){var a=i in this._layers?i:this.getLayerId(i);return this._map&&this._layers[a]&&this._map.removeLayer(this._layers[a]),delete this._layers[a],this},hasLayer:function(i){var a=typeof i=="number"?i:this.getLayerId(i);return a in this._layers},clearLayers:function(){return this.eachLayer(this.removeLayer,this)},invoke:function(i){var a=Array.prototype.slice.call(arguments,1),s,r;for(s in this._layers)r=this._layers[s],r[i]&&r[i].apply(r,a);return this},onAdd:function(i){this.eachLayer(i.addLayer,i)},onRemove:function(i){this.eachLayer(i.removeLayer,i)},eachLayer:function(i,a){for(var s in this._layers)i.call(a,this._layers[s]);return this},getLayer:function(i){return this._layers[i]},getLayers:function(){var i=[];return this.eachLayer(i.push,i),i},setZIndex:function(i){return this.invoke("setZIndex",i)},getLayerId:function(i){return A(i)}}),po=function(i,a){return new nn(i,a)},Ne=nn.extend({addLayer:function(i){return this.hasLayer(i)?this:(i.addEventParent(this),nn.prototype.addLayer.call(this,i),this.fire("layeradd",{layer:i}))},removeLayer:function(i){return this.hasLayer(i)?(i in this._layers&&(i=this._layers[i]),i.removeEventParent(this),nn.prototype.removeLayer.call(this,i),this.fire("layerremove",{layer:i})):this},setStyle:function(i){return this.invoke("setStyle",i)},bringToFront:function(){return this.invoke("bringToFront")},bringToBack:function(){return this.invoke("bringToBack")},getBounds:function(){var i=new G;for(var a in this._layers){var s=this._layers[a];i.extend(s.getBounds?s.getBounds():s.getLatLng())}return i}}),Ma=function(i,a){return new Ne(i,a)},Zn=de.extend({options:{popupAnchor:[0,0],tooltipAnchor:[0,0],crossOrigin:!1},initialize:function(i){mt(this,i)},createIcon:function(i){return this._createIcon("icon",i)},createShadow:function(i){return this._createIcon("shadow",i)},_createIcon:function(i,a){var s=this._getIconUrl(i);if(!s){if(i==="icon")throw new Error("iconUrl not set in Icon options (see the docs).");return null}var r=this._createImg(s,a&&a.tagName==="IMG"?a:null);return this._setIconStyles(r,i),(this.options.crossOrigin||this.options.crossOrigin==="")&&(r.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),r},_setIconStyles:function(i,a){var s=this.options,r=s[a+"Size"];typeof r=="number"&&(r=[r,r]);var c=m(r),h=m(a==="shadow"&&s.shadowAnchor||s.iconAnchor||c&&c.divideBy(2,!0));i.className="leaflet-marker-"+a+" "+(s.className||""),h&&(i.style.marginLeft=-h.x+"px",i.style.marginTop=-h.y+"px"),c&&(i.style.width=c.x+"px",i.style.height=c.y+"px")},_createImg:function(i,a){return a=a||document.createElement("img"),a.src=i,a},_getIconUrl:function(i){return et.retina&&this.options[i+"RetinaUrl"]||this.options[i+"Url"]}});function ll(i){return new Zn(i)}var jn=Zn.extend({options:{iconUrl:"marker-icon.png",iconRetinaUrl:"marker-icon-2x.png",shadowUrl:"marker-shadow.png",iconSize:[25,41],iconAnchor:[12,41],popupAnchor:[1,-34],tooltipAnchor:[16,-28],shadowSize:[41,41]},_getIconUrl:function(i){return typeof jn.imagePath!="string"&&(jn.imagePath=this._detectIconPath()),(this.options.imagePath||jn.imagePath)+Zn.prototype._getIconUrl.call(this,i)},_stripUrl:function(i){var a=function(s,r,c){var h=r.exec(s);return h&&h[c]};return i=a(i,/^url\((['"])?(.+)\1\)$/,2),i&&a(i,/^(.*)marker-icon\.png$/,1)},_detectIconPath:function(){var i=yt("div","leaflet-default-icon-path",document.body),a=Ki(i,"background-image")||Ki(i,"backgroundImage");if(document.body.removeChild(i),a=this._stripUrl(a),a)return a;var s=document.querySelector('link[href$="leaflet.css"]');return s?s.href.substring(0,s.href.length-11-1):""}}),$l=Xe.extend({initialize:function(i){this._marker=i},addHooks:function(){var i=this._marker._icon;this._draggable||(this._draggable=new Oi(i,i,!0)),this._draggable.on({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).enable(),rt(i,"leaflet-marker-draggable")},removeHooks:function(){this._draggable.off({dragstart:this._onDragStart,predrag:this._onPreDrag,drag:this._onDrag,dragend:this._onDragEnd},this).disable(),this._marker._icon&&Rt(this._marker._icon,"leaflet-marker-draggable")},moved:function(){return this._draggable&&this._draggable._moved},_adjustPan:function(i){var a=this._marker,s=a._map,r=this._marker.options.autoPanSpeed,c=this._marker.options.autoPanPadding,h=Li(a._icon),_=s.getPixelBounds(),T=s.getPixelOrigin(),N=Y(_.min._subtract(T).add(c),_.max._subtract(T).subtract(c));if(!N.contains(h)){var H=m((Math.max(N.max.x,h.x)-N.max.x)/(_.max.x-N.max.x)-(Math.min(N.min.x,h.x)-N.min.x)/(_.min.x-N.min.x),(Math.max(N.max.y,h.y)-N.max.y)/(_.max.y-N.max.y)-(Math.min(N.min.y,h.y)-N.min.y)/(_.min.y-N.min.y)).multiplyBy(r);s.panBy(H,{animate:!1}),this._draggable._newPos._add(H),this._draggable._startPos._add(H),Gt(a._icon,this._draggable._newPos),this._onDrag(i),this._panRequest=Ut(this._adjustPan.bind(this,i))}},_onDragStart:function(){this._oldLatLng=this._marker.getLatLng(),this._marker.closePopup&&this._marker.closePopup(),this._marker.fire("movestart").fire("dragstart")},_onPreDrag:function(i){this._marker.options.autoPan&&(Et(this._panRequest),this._panRequest=Ut(this._adjustPan.bind(this,i)))},_onDrag:function(i){var a=this._marker,s=a._shadow,r=Li(a._icon),c=a._map.layerPointToLatLng(r);s&&Gt(s,r),a._latlng=c,i.latlng=c,i.oldLatLng=this._oldLatLng,a.fire("move",i).fire("drag",i)},_onDragEnd:function(i){Et(this._panRequest),delete this._oldLatLng,this._marker.fire("moveend").fire("dragend",i)}}),Un=Qe.extend({options:{icon:new jn,interactive:!0,keyboard:!0,title:"",alt:"Marker",zIndexOffset:0,opacity:1,riseOnHover:!1,riseOffset:250,pane:"markerPane",shadowPane:"shadowPane",bubblingMouseEvents:!1,autoPanOnFocus:!0,draggable:!1,autoPan:!1,autoPanPadding:[50,50],autoPanSpeed:10},initialize:function(i,a){mt(this,a),this._latlng=X(i)},onAdd:function(i){this._zoomAnimated=this._zoomAnimated&&i.options.markerZoomAnimation,this._zoomAnimated&&i.on("zoomanim",this._animateZoom,this),this._initIcon(),this.update()},onRemove:function(i){this.dragging&&this.dragging.enabled()&&(this.options.draggable=!0,this.dragging.removeHooks()),delete this.dragging,this._zoomAnimated&&i.off("zoomanim",this._animateZoom,this),this._removeIcon(),this._removeShadow()},getEvents:function(){return{zoom:this.update,viewreset:this.update}},getLatLng:function(){return this._latlng},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.update(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},setZIndexOffset:function(i){return this.options.zIndexOffset=i,this.update()},getIcon:function(){return this.options.icon},setIcon:function(i){return this.options.icon=i,this._map&&(this._initIcon(),this.update()),this._popup&&this.bindPopup(this._popup,this._popup.options),this},getElement:function(){return this._icon},update:function(){if(this._icon&&this._map){var i=this._map.latLngToLayerPoint(this._latlng).round();this._setPos(i)}return this},_initIcon:function(){var i=this.options,a="leaflet-zoom-"+(this._zoomAnimated?"animated":"hide"),s=i.icon.createIcon(this._icon),r=!1;s!==this._icon&&(this._icon&&this._removeIcon(),r=!0,i.title&&(s.title=i.title),s.tagName==="IMG"&&(s.alt=i.alt||"")),rt(s,a),i.keyboard&&(s.tabIndex="0",s.setAttribute("role","button")),this._icon=s,i.riseOnHover&&this.on({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&ot(s,"focus",this._panOnFocus,this);var c=i.icon.createShadow(this._shadow),h=!1;c!==this._shadow&&(this._removeShadow(),h=!0),c&&(rt(c,a),c.alt=""),this._shadow=c,i.opacity<1&&this._updateOpacity(),r&&this.getPane().appendChild(this._icon),this._initInteraction(),c&&h&&this.getPane(i.shadowPane).appendChild(this._shadow)},_removeIcon:function(){this.options.riseOnHover&&this.off({mouseover:this._bringToFront,mouseout:this._resetZIndex}),this.options.autoPanOnFocus&&St(this._icon,"focus",this._panOnFocus,this),Dt(this._icon),this.removeInteractiveTarget(this._icon),this._icon=null},_removeShadow:function(){this._shadow&&Dt(this._shadow),this._shadow=null},_setPos:function(i){this._icon&&Gt(this._icon,i),this._shadow&&Gt(this._shadow,i),this._zIndex=i.y+this.options.zIndexOffset,this._resetZIndex()},_updateZIndex:function(i){this._icon&&(this._icon.style.zIndex=this._zIndex+i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center).round();this._setPos(a)},_initInteraction:function(){if(this.options.interactive&&(rt(this._icon,"leaflet-interactive"),this.addInteractiveTarget(this._icon),$l)){var i=this.options.draggable;this.dragging&&(i=this.dragging.enabled(),this.dragging.disable()),this.dragging=new $l(this),i&&this.dragging.enable()}},setOpacity:function(i){return this.options.opacity=i,this._map&&this._updateOpacity(),this},_updateOpacity:function(){var i=this.options.opacity;this._icon&&we(this._icon,i),this._shadow&&we(this._shadow,i)},_bringToFront:function(){this._updateZIndex(this.options.riseOffset)},_resetZIndex:function(){this._updateZIndex(0)},_panOnFocus:function(){var i=this._map;if(i){var a=this.options.icon.options,s=a.iconSize?m(a.iconSize):m(0,0),r=a.iconAnchor?m(a.iconAnchor):m(0,0);i.panInside(this._latlng,{paddingTopLeft:r,paddingBottomRight:s.subtract(r)})}},_getPopupAnchor:function(){return this.options.icon.options.popupAnchor},_getTooltipAnchor:function(){return this.options.icon.options.tooltipAnchor}});function ts(i,a){return new Un(i,a)}var yi=Qe.extend({options:{stroke:!0,color:"#3388ff",weight:3,opacity:1,lineCap:"round",lineJoin:"round",dashArray:null,dashOffset:null,fill:!1,fillColor:null,fillOpacity:.2,fillRule:"evenodd",interactive:!0,bubblingMouseEvents:!0},beforeAdd:function(i){this._renderer=i.getRenderer(this)},onAdd:function(){this._renderer._initPath(this),this._reset(),this._renderer._addPath(this)},onRemove:function(){this._renderer._removePath(this)},redraw:function(){return this._map&&this._renderer._updatePath(this),this},setStyle:function(i){return mt(this,i),this._renderer&&(this._renderer._updateStyle(this),this.options.stroke&&i&&Object.prototype.hasOwnProperty.call(i,"weight")&&this._updateBounds()),this},bringToFront:function(){return this._renderer&&this._renderer._bringToFront(this),this},bringToBack:function(){return this._renderer&&this._renderer._bringToBack(this),this},getElement:function(){return this._path},_reset:function(){this._project(),this._update()},_clickTolerance:function(){return(this.options.stroke?this.options.weight/2:0)+(this._renderer.options.tolerance||0)}}),Ea=yi.extend({options:{fill:!0,radius:10},initialize:function(i,a){mt(this,a),this._latlng=X(i),this._radius=this.options.radius},setLatLng:function(i){var a=this._latlng;return this._latlng=X(i),this.redraw(),this.fire("move",{oldLatLng:a,latlng:this._latlng})},getLatLng:function(){return this._latlng},setRadius:function(i){return this.options.radius=this._radius=i,this.redraw()},getRadius:function(){return this._radius},setStyle:function(i){var a=i&&i.radius||this._radius;return yi.prototype.setStyle.call(this,i),this.setRadius(a),this},_project:function(){this._point=this._map.latLngToLayerPoint(this._latlng),this._updateBounds()},_updateBounds:function(){var i=this._radius,a=this._radiusY||i,s=this._clickTolerance(),r=[i+s,a+s];this._pxBounds=new R(this._point.subtract(r),this._point.add(r))},_update:function(){this._map&&this._updatePath()},_updatePath:function(){this._renderer._updateCircle(this)},_empty:function(){return this._radius&&!this._renderer._bounds.intersects(this._pxBounds)},_containsPoint:function(i){return i.distanceTo(this._point)<=this._radius+this._clickTolerance()}});function vo(i,a){return new Ea(i,a)}var es=Ea.extend({initialize:function(i,a,s){if(typeof a=="number"&&(a=B({},s,{radius:a})),mt(this,a),this._latlng=X(i),isNaN(this.options.radius))throw new Error("Circle radius cannot be NaN");this._mRadius=this.options.radius},setRadius:function(i){return this._mRadius=i,this.redraw()},getRadius:function(){return this._mRadius},getBounds:function(){var i=[this._radius,this._radiusY||this._radius];return new G(this._map.layerPointToLatLng(this._point.subtract(i)),this._map.layerPointToLatLng(this._point.add(i)))},setStyle:yi.prototype.setStyle,_project:function(){var i=this._latlng.lng,a=this._latlng.lat,s=this._map,r=s.options.crs;if(r.distance===xt.distance){var c=Math.PI/180,h=this._mRadius/xt.R/c,_=s.project([a+h,i]),T=s.project([a-h,i]),N=_.add(T).divideBy(2),H=s.unproject(N).lat,K=Math.acos((Math.cos(h*c)-Math.sin(a*c)*Math.sin(H*c))/(Math.cos(a*c)*Math.cos(H*c)))/c;(isNaN(K)||K===0)&&(K=h/Math.cos(Math.PI/180*a)),this._point=N.subtract(s.getPixelOrigin()),this._radius=isNaN(K)?0:N.x-s.project([H,i-K]).x,this._radiusY=N.y-_.y}else{var W=r.unproject(r.project(this._latlng).subtract([this._mRadius,0]));this._point=s.latLngToLayerPoint(this._latlng),this._radius=this._point.x-s.latLngToLayerPoint(W).x}this._updateBounds()}});function Uu(i,a,s){return new es(i,a,s)}var bi=yi.extend({options:{smoothFactor:1,noClip:!1},initialize:function(i,a){mt(this,a),this._setLatLngs(i)},getLatLngs:function(){return this._latlngs},setLatLngs:function(i){return this._setLatLngs(i),this.redraw()},isEmpty:function(){return!this._latlngs.length},closestLayerPoint:function(i){for(var a=1/0,s=null,r=en,c,h,_=0,T=this._parts.length;_<T;_++)for(var N=this._parts[_],H=1,K=N.length;H<K;H++){c=N[H-1],h=N[H];var W=r(i,c,h,!0);W<a&&(a=W,s=r(i,c,h))}return s&&(s.distance=Math.sqrt(a)),s},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return Il(this._defaultShape(),this._map.options.crs)},getBounds:function(){return this._bounds},addLatLng:function(i,a){return a=a||this._defaultShape(),i=X(i),a.push(i),this._bounds.extend(i),this.redraw()},_setLatLngs:function(i){this._bounds=new G,this._latlngs=this._convertLatLngs(i)},_defaultShape:function(){return se(this._latlngs)?this._latlngs:this._latlngs[0]},_convertLatLngs:function(i){for(var a=[],s=se(i),r=0,c=i.length;r<c;r++)s?(a[r]=X(i[r]),this._bounds.extend(a[r])):a[r]=this._convertLatLngs(i[r]);return a},_project:function(){var i=new R;this._rings=[],this._projectLatlngs(this._latlngs,this._rings,i),this._bounds.isValid()&&i.isValid()&&(this._rawPxBounds=i,this._updateBounds())},_updateBounds:function(){var i=this._clickTolerance(),a=new P(i,i);this._rawPxBounds&&(this._pxBounds=new R([this._rawPxBounds.min.subtract(a),this._rawPxBounds.max.add(a)]))},_projectLatlngs:function(i,a,s){var r=i[0]instanceof $,c=i.length,h,_;if(r){for(_=[],h=0;h<c;h++)_[h]=this._map.latLngToLayerPoint(i[h]),s.extend(_[h]);a.push(_)}else for(h=0;h<c;h++)this._projectLatlngs(i[h],a,s)},_clipPoints:function(){var i=this._renderer._bounds;if(this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}var a=this._parts,s,r,c,h,_,T,N;for(s=0,c=0,h=this._rings.length;s<h;s++)for(N=this._rings[s],r=0,_=N.length;r<_-1;r++)T=co(N[r],N[r+1],i,r,!0),T&&(a[c]=a[c]||[],a[c].push(T[0]),(T[1]!==N[r+1]||r===_-2)&&(a[c].push(T[1]),c++))}},_simplifyPoints:function(){for(var i=this._parts,a=this.options.smoothFactor,s=0,r=i.length;s<r;s++)i[s]=uo(i[s],a)},_update:function(){this._map&&(this._clipPoints(),this._simplifyPoints(),this._updatePath())},_updatePath:function(){this._renderer._updatePoly(this)},_containsPoint:function(i,a){var s,r,c,h,_,T,N=this._clickTolerance();if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(s=0,h=this._parts.length;s<h;s++)for(T=this._parts[s],r=0,_=T.length,c=_-1;r<_;c=r++)if(!(!a&&r===0)&&Wl(i,T[c],T[r])<=N)return!0;return!1}});function Hu(i,a){return new bi(i,a)}bi._flat=fo;var Hn=bi.extend({options:{fill:!0},isEmpty:function(){return!this._latlngs.length||!this._latlngs[0].length},getCenter:function(){if(!this._map)throw new Error("Must add layer to map before using getCenter()");return el(this._defaultShape(),this._map.options.crs)},_convertLatLngs:function(i){var a=bi.prototype._convertLatLngs.call(this,i),s=a.length;return s>=2&&a[0]instanceof $&&a[0].equals(a[s-1])&&a.pop(),a},_setLatLngs:function(i){bi.prototype._setLatLngs.call(this,i),se(this._latlngs)&&(this._latlngs=[this._latlngs])},_defaultShape:function(){return se(this._latlngs[0])?this._latlngs[0]:this._latlngs[0][0]},_clipPoints:function(){var i=this._renderer._bounds,a=this.options.weight,s=new P(a,a);if(i=new R(i.min.subtract(s),i.max.add(s)),this._parts=[],!(!this._pxBounds||!this._pxBounds.intersects(i))){if(this.options.noClip){this._parts=this._rings;return}for(var r=0,c=this._rings.length,h;r<c;r++)h=oo(this._rings[r],i,!0),h.length&&this._parts.push(h)}},_updatePath:function(){this._renderer._updatePoly(this,!0)},_containsPoint:function(i){var a=!1,s,r,c,h,_,T,N,H;if(!this._pxBounds||!this._pxBounds.contains(i))return!1;for(h=0,N=this._parts.length;h<N;h++)for(s=this._parts[h],_=0,H=s.length,T=H-1;_<H;T=_++)r=s[_],c=s[T],r.y>i.y!=c.y>i.y&&i.x<(c.x-r.x)*(i.y-r.y)/(c.y-r.y)+r.x&&(a=!a);return a||bi.prototype._containsPoint.call(this,i,!0)}});function Ce(i,a){return new Hn(i,a)}var De=Ne.extend({initialize:function(i,a){mt(this,a),this._layers={},i&&this.addData(i)},addData:function(i){var a=wt(i)?i:i.features,s,r,c;if(a){for(s=0,r=a.length;s<r;s++)c=a[s],(c.geometries||c.geometry||c.features||c.coordinates)&&this.addData(c);return this}var h=this.options;if(h.filter&&!h.filter(i))return this;var _=za(i,h);return _?(_.feature=qn(i),_.defaultOptions=_.options,this.resetStyle(_),h.onEachFeature&&h.onEachFeature(i,_),this.addLayer(_)):this},resetStyle:function(i){return i===void 0?this.eachLayer(this.resetStyle,this):(i.options=B({},i.defaultOptions),this._setLayerStyle(i,this.options.style),this)},setStyle:function(i){return this.eachLayer(function(a){this._setLayerStyle(a,i)},this)},_setLayerStyle:function(i,a){i.setStyle&&(typeof a=="function"&&(a=a(i.feature)),i.setStyle(a))}});function za(i,a){var s=i.type==="Feature"?i.geometry:i,r=s?s.coordinates:null,c=[],h=a&&a.pointToLayer,_=a&&a.coordsToLatLng||sl,T,N,H,K;if(!r&&!s)return null;switch(s.type){case"Point":return T=_(r),is(h,i,T,a);case"MultiPoint":for(H=0,K=r.length;H<K;H++)T=_(r[H]),c.push(is(h,i,T,a));return new Ne(c);case"LineString":case"MultiLineString":return N=La(r,s.type==="LineString"?0:1,_),new bi(N,a);case"Polygon":case"MultiPolygon":return N=La(r,s.type==="Polygon"?1:2,_),new Hn(N,a);case"GeometryCollection":for(H=0,K=s.geometries.length;H<K;H++){var W=za({geometry:s.geometries[H],type:"Feature",properties:i.properties},a);W&&c.push(W)}return new Ne(c);case"FeatureCollection":for(H=0,K=s.features.length;H<K;H++){var tt=za(s.features[H],a);tt&&c.push(tt)}return new Ne(c);default:throw new Error("Invalid GeoJSON object.")}}function is(i,a,s,r){return i?i(a,s):new Un(s,r&&r.markersInheritOptions&&r)}function sl(i){return new $(i[1],i[0],i[2])}function La(i,a,s){for(var r=[],c=0,h=i.length,_;c<h;c++)_=a?La(i[c],a-1,s):(s||sl)(i[c]),r.push(_);return r}function Aa(i,a){return i=X(i),i.alt!==void 0?[ut(i.lng,a),ut(i.lat,a),ut(i.alt,a)]:[ut(i.lng,a),ut(i.lat,a)]}function ol(i,a,s,r){for(var c=[],h=0,_=i.length;h<_;h++)c.push(a?ol(i[h],se(i[h])?0:a-1,s,r):Aa(i[h],r));return!a&&s&&c.length>0&&c.push(c[0].slice()),c}function Ke(i,a){return i.feature?B({},i.feature,{geometry:a}):qn(a)}function qn(i){return i.type==="Feature"||i.type==="FeatureCollection"?i:{type:"Feature",properties:{},geometry:i}}var an={toGeoJSON:function(i){return Ke(this,{type:"Point",coordinates:Aa(this.getLatLng(),i)})}};Un.include(an),es.include(an),Ea.include(an),bi.include({toGeoJSON:function(i){var a=!se(this._latlngs),s=ol(this._latlngs,a?1:0,!1,i);return Ke(this,{type:(a?"Multi":"")+"LineString",coordinates:s})}}),Hn.include({toGeoJSON:function(i){var a=!se(this._latlngs),s=a&&!se(this._latlngs[0]),r=ol(this._latlngs,s?2:a?1:0,!0,i);return a||(r=[r]),Ke(this,{type:(s?"Multi":"")+"Polygon",coordinates:r})}}),nn.include({toMultiPoint:function(i){var a=[];return this.eachLayer(function(s){a.push(s.toGeoJSON(i).geometry.coordinates)}),Ke(this,{type:"MultiPoint",coordinates:a})},toGeoJSON:function(i){var a=this.feature&&this.feature.geometry&&this.feature.geometry.type;if(a==="MultiPoint")return this.toMultiPoint(i);var s=a==="GeometryCollection",r=[];return this.eachLayer(function(c){if(c.toGeoJSON){var h=c.toGeoJSON(i);if(s)r.push(h.geometry);else{var _=qn(h);_.type==="FeatureCollection"?r.push.apply(r,_.features):r.push(_)}}}),s?Ke(this,{geometries:r,type:"GeometryCollection"}):{type:"FeatureCollection",features:r}}});function ul(i,a){return new De(i,a)}var go=ul,ui=Qe.extend({options:{opacity:1,alt:"",interactive:!1,crossOrigin:!1,errorOverlayUrl:"",zIndex:1,className:""},initialize:function(i,a,s){this._url=i,this._bounds=J(a),mt(this,s)},onAdd:function(){this._image||(this._initImage(),this.options.opacity<1&&this._updateOpacity()),this.options.interactive&&(rt(this._image,"leaflet-interactive"),this.addInteractiveTarget(this._image)),this.getPane().appendChild(this._image),this._reset()},onRemove:function(){Dt(this._image),this.options.interactive&&this.removeInteractiveTarget(this._image)},setOpacity:function(i){return this.options.opacity=i,this._image&&this._updateOpacity(),this},setStyle:function(i){return i.opacity&&this.setOpacity(i.opacity),this},bringToFront:function(){return this._map&&Ji(this._image),this},bringToBack:function(){return this._map&&En(this._image),this},setUrl:function(i){return this._url=i,this._image&&(this._image.src=i),this},setBounds:function(i){return this._bounds=J(i),this._map&&this._reset(),this},getEvents:function(){var i={zoom:this._reset,viewreset:this._reset};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},getBounds:function(){return this._bounds},getElement:function(){return this._image},_initImage:function(){var i=this._url.tagName==="IMG",a=this._image=i?this._url:yt("img");if(rt(a,"leaflet-image-layer"),this._zoomAnimated&&rt(a,"leaflet-zoom-animated"),this.options.className&&rt(a,this.options.className),a.onselectstart=I,a.onmousemove=I,a.onload=k(this.fire,this,"load"),a.onerror=k(this._overlayOnError,this,"error"),(this.options.crossOrigin||this.options.crossOrigin==="")&&(a.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),this.options.zIndex&&this._updateZIndex(),i){this._url=a.src;return}a.src=this._url,a.alt=this.options.alt},_animateZoom:function(i){var a=this._map.getZoomScale(i.zoom),s=this._map._latLngBoundsToNewLayerBounds(this._bounds,i.zoom,i.center).min;qe(this._image,s,a)},_reset:function(){var i=this._image,a=new R(this._map.latLngToLayerPoint(this._bounds.getNorthWest()),this._map.latLngToLayerPoint(this._bounds.getSouthEast())),s=a.getSize();Gt(i,a.min),i.style.width=s.x+"px",i.style.height=s.y+"px"},_updateOpacity:function(){we(this._image,this.options.opacity)},_updateZIndex:function(){this._image&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._image.style.zIndex=this.options.zIndex)},_overlayOnError:function(){this.fire("error");var i=this.options.errorOverlayUrl;i&&this._url!==i&&(this._url=i,this._image.src=i)},getCenter:function(){return this._bounds.getCenter()}}),kn=function(i,a,s){return new ui(i,a,s)},rl=ui.extend({options:{autoplay:!0,loop:!0,keepAspectRatio:!0,muted:!1,playsInline:!0},_initImage:function(){var i=this._url.tagName==="VIDEO",a=this._image=i?this._url:yt("video");if(rt(a,"leaflet-image-layer"),this._zoomAnimated&&rt(a,"leaflet-zoom-animated"),this.options.className&&rt(a,this.options.className),a.onselectstart=I,a.onmousemove=I,a.onloadeddata=k(this.fire,this,"load"),i){for(var s=a.getElementsByTagName("source"),r=[],c=0;c<s.length;c++)r.push(s[c].src);this._url=s.length>0?r:[a.src];return}wt(this._url)||(this._url=[this._url]),!this.options.keepAspectRatio&&Object.prototype.hasOwnProperty.call(a.style,"objectFit")&&(a.style.objectFit="fill"),a.autoplay=!!this.options.autoplay,a.loop=!!this.options.loop,a.muted=!!this.options.muted,a.playsInline=!!this.options.playsInline;for(var h=0;h<this._url.length;h++){var _=yt("source");_.src=this._url[h],a.appendChild(_)}}});function yo(i,a,s){return new rl(i,a,s)}var Ni=ui.extend({_initImage:function(){var i=this._image=this._url;rt(i,"leaflet-image-layer"),this._zoomAnimated&&rt(i,"leaflet-zoom-animated"),this.options.className&&rt(i,this.options.className),i.onselectstart=I,i.onmousemove=I}});function bo(i,a,s){return new Ni(i,a,s)}var Je=Qe.extend({options:{interactive:!1,offset:[0,0],className:"",pane:void 0,content:""},initialize:function(i,a){i&&(i instanceof $||wt(i))?(this._latlng=X(i),mt(this,a)):(mt(this,i),this._source=a),this.options.content&&(this._content=this.options.content)},openOn:function(i){return i=arguments.length?i:this._source._map,i.hasLayer(this)||i.addLayer(this),this},close:function(){return this._map&&this._map.removeLayer(this),this},toggle:function(i){return this._map?this.close():(arguments.length?this._source=i:i=this._source,this._prepareOpen(),this.openOn(i._map)),this},onAdd:function(i){this._zoomAnimated=i._zoomAnimated,this._container||this._initLayout(),i._fadeAnimated&&we(this._container,0),clearTimeout(this._removeTimeout),this.getPane().appendChild(this._container),this.update(),i._fadeAnimated&&we(this._container,1),this.bringToFront(),this.options.interactive&&(rt(this._container,"leaflet-interactive"),this.addInteractiveTarget(this._container))},onRemove:function(i){i._fadeAnimated?(we(this._container,0),this._removeTimeout=setTimeout(k(Dt,void 0,this._container),200)):Dt(this._container),this.options.interactive&&(Rt(this._container,"leaflet-interactive"),this.removeInteractiveTarget(this._container))},getLatLng:function(){return this._latlng},setLatLng:function(i){return this._latlng=X(i),this._map&&(this._updatePosition(),this._adjustPan()),this},getContent:function(){return this._content},setContent:function(i){return this._content=i,this.update(),this},getElement:function(){return this._container},update:function(){this._map&&(this._container.style.visibility="hidden",this._updateContent(),this._updateLayout(),this._updatePosition(),this._container.style.visibility="",this._adjustPan())},getEvents:function(){var i={zoom:this._updatePosition,viewreset:this._updatePosition};return this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},isOpen:function(){return!!this._map&&this._map.hasLayer(this)},bringToFront:function(){return this._map&&Ji(this._container),this},bringToBack:function(){return this._map&&En(this._container),this},_prepareOpen:function(i){var a=this._source;if(!a._map)return!1;if(a instanceof Ne){a=null;var s=this._source._layers;for(var r in s)if(s[r]._map){a=s[r];break}if(!a)return!1;this._source=a}if(!i)if(a.getCenter)i=a.getCenter();else if(a.getLatLng)i=a.getLatLng();else if(a.getBounds)i=a.getBounds().getCenter();else throw new Error("Unable to get source layer LatLng.");return this.setLatLng(i),this._map&&this.update(),!0},_updateContent:function(){if(this._content){var i=this._contentNode,a=typeof this._content=="function"?this._content(this._source||this):this._content;if(typeof a=="string")i.innerHTML=a;else{for(;i.hasChildNodes();)i.removeChild(i.firstChild);i.appendChild(a)}this.fire("contentupdate")}},_updatePosition:function(){if(this._map){var i=this._map.latLngToLayerPoint(this._latlng),a=m(this.options.offset),s=this._getAnchor();this._zoomAnimated?Gt(this._container,i.add(s)):a=a.add(i).add(s);var r=this._containerBottom=-a.y,c=this._containerLeft=-Math.round(this._containerWidth/2)+a.x;this._container.style.bottom=r+"px",this._container.style.left=c+"px"}},_getAnchor:function(){return[0,0]}});_t.include({_initOverlay:function(i,a,s,r){var c=a;return c instanceof i||(c=new i(r).setContent(a)),s&&c.setLatLng(s),c}}),Qe.include({_initOverlay:function(i,a,s,r){var c=s;return c instanceof i?(mt(c,r),c._source=this):(c=a&&!r?a:new i(r,this),c.setContent(s)),c}});var Oa=Je.extend({options:{pane:"popupPane",offset:[0,7],maxWidth:300,minWidth:50,maxHeight:null,autoPan:!0,autoPanPaddingTopLeft:null,autoPanPaddingBottomRight:null,autoPanPadding:[5,5],keepInView:!1,closeButton:!0,autoClose:!0,closeOnEscapeKey:!0,className:""},openOn:function(i){return i=arguments.length?i:this._source._map,!i.hasLayer(this)&&i._popup&&i._popup.options.autoClose&&i.removeLayer(i._popup),i._popup=this,Je.prototype.openOn.call(this,i)},onAdd:function(i){Je.prototype.onAdd.call(this,i),i.fire("popupopen",{popup:this}),this._source&&(this._source.fire("popupopen",{popup:this},!0),this._source instanceof yi||this._source.on("preclick",gi))},onRemove:function(i){Je.prototype.onRemove.call(this,i),i.fire("popupclose",{popup:this}),this._source&&(this._source.fire("popupclose",{popup:this},!0),this._source instanceof yi||this._source.off("preclick",gi))},getEvents:function(){var i=Je.prototype.getEvents.call(this);return(this.options.closeOnClick!==void 0?this.options.closeOnClick:this._map.options.closePopupOnClick)&&(i.preclick=this.close),this.options.keepInView&&(i.moveend=this._adjustPan),i},_initLayout:function(){var i="leaflet-popup",a=this._container=yt("div",i+" "+(this.options.className||"")+" leaflet-zoom-animated"),s=this._wrapper=yt("div",i+"-content-wrapper",a);if(this._contentNode=yt("div",i+"-content",s),Dn(a),Cn(this._contentNode),ot(a,"contextmenu",gi),this._tipContainer=yt("div",i+"-tip-container",a),this._tip=yt("div",i+"-tip",this._tipContainer),this.options.closeButton){var r=this._closeButton=yt("a",i+"-close-button",a);r.setAttribute("role","button"),r.setAttribute("aria-label","Close popup"),r.href="#close",r.innerHTML='<span aria-hidden="true">&#215;</span>',ot(r,"click",function(c){Qt(c),this.close()},this)}},_updateLayout:function(){var i=this._contentNode,a=i.style;a.width="",a.whiteSpace="nowrap";var s=i.offsetWidth;s=Math.min(s,this.options.maxWidth),s=Math.max(s,this.options.minWidth),a.width=s+1+"px",a.whiteSpace="",a.height="";var r=i.offsetHeight,c=this.options.maxHeight,h="leaflet-popup-scrolled";c&&r>c?(a.height=c+"px",rt(i,h)):Rt(i,h),this._containerWidth=this._container.offsetWidth},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center),s=this._getAnchor();Gt(this._container,a.add(s))},_adjustPan:function(){if(this.options.autoPan){if(this._map._panAnim&&this._map._panAnim.stop(),this._autopanning){this._autopanning=!1;return}var i=this._map,a=parseInt(Ki(this._container,"marginBottom"),10)||0,s=this._container.offsetHeight+a,r=this._containerWidth,c=new P(this._containerLeft,-s-this._containerBottom);c._add(Li(this._container));var h=i.layerPointToContainerPoint(c),_=m(this.options.autoPanPadding),T=m(this.options.autoPanPaddingTopLeft||_),N=m(this.options.autoPanPaddingBottomRight||_),H=i.getSize(),K=0,W=0;h.x+r+N.x>H.x&&(K=h.x+r-H.x+N.x),h.x-K-T.x<0&&(K=h.x-T.x),h.y+s+N.y>H.y&&(W=h.y+s-H.y+N.y),h.y-W-T.y<0&&(W=h.y-T.y),(K||W)&&(this.options.keepInView&&(this._autopanning=!0),i.fire("autopanstart").panBy([K,W]))}},_getAnchor:function(){return m(this._source&&this._source._getPopupAnchor?this._source._getPopupAnchor():[0,0])}}),qu=function(i,a){return new Oa(i,a)};_t.mergeOptions({closePopupOnClick:!0}),_t.include({openPopup:function(i,a,s){return this._initOverlay(Oa,i,a,s).openOn(this),this},closePopup:function(i){return i=arguments.length?i:this._popup,i&&i.close(),this}}),Qe.include({bindPopup:function(i,a){return this._popup=this._initOverlay(Oa,this._popup,i,a),this._popupHandlersAdded||(this.on({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!0),this},unbindPopup:function(){return this._popup&&(this.off({click:this._openPopup,keypress:this._onKeyPress,remove:this.closePopup,move:this._movePopup}),this._popupHandlersAdded=!1,this._popup=null),this},openPopup:function(i){return this._popup&&(this instanceof Ne||(this._popup._source=this),this._popup._prepareOpen(i||this._latlng)&&this._popup.openOn(this._map)),this},closePopup:function(){return this._popup&&this._popup.close(),this},togglePopup:function(){return this._popup&&this._popup.toggle(this),this},isPopupOpen:function(){return this._popup?this._popup.isOpen():!1},setPopupContent:function(i){return this._popup&&this._popup.setContent(i),this},getPopup:function(){return this._popup},_openPopup:function(i){if(!(!this._popup||!this._map)){oi(i);var a=i.layer||i.target;if(this._popup._source===a&&!(a instanceof yi)){this._map.hasLayer(this._popup)?this.closePopup():this.openPopup(i.latlng);return}this._popup._source=a,this.openPopup(i.latlng)}},_movePopup:function(i){this._popup.setLatLng(i.latlng)},_onKeyPress:function(i){i.originalEvent.keyCode===13&&this._openPopup(i)}});var cl=Je.extend({options:{pane:"tooltipPane",offset:[0,0],direction:"auto",permanent:!1,sticky:!1,opacity:.9},onAdd:function(i){Je.prototype.onAdd.call(this,i),this.setOpacity(this.options.opacity),i.fire("tooltipopen",{tooltip:this}),this._source&&(this.addEventParent(this._source),this._source.fire("tooltipopen",{tooltip:this},!0))},onRemove:function(i){Je.prototype.onRemove.call(this,i),i.fire("tooltipclose",{tooltip:this}),this._source&&(this.removeEventParent(this._source),this._source.fire("tooltipclose",{tooltip:this},!0))},getEvents:function(){var i=Je.prototype.getEvents.call(this);return this.options.permanent||(i.preclick=this.close),i},_initLayout:function(){var i="leaflet-tooltip",a=i+" "+(this.options.className||"")+" leaflet-zoom-"+(this._zoomAnimated?"animated":"hide");this._contentNode=this._container=yt("div",a),this._container.setAttribute("role","tooltip"),this._container.setAttribute("id","leaflet-tooltip-"+A(this))},_updateLayout:function(){},_adjustPan:function(){},_setPosition:function(i){var a,s,r=this._map,c=this._container,h=r.latLngToContainerPoint(r.getCenter()),_=r.layerPointToContainerPoint(i),T=this.options.direction,N=c.offsetWidth,H=c.offsetHeight,K=m(this.options.offset),W=this._getAnchor();T==="top"?(a=N/2,s=H):T==="bottom"?(a=N/2,s=0):T==="center"?(a=N/2,s=H/2):T==="right"?(a=0,s=H/2):T==="left"?(a=N,s=H/2):_.x<h.x?(T="right",a=0,s=H/2):(T="left",a=N+(K.x+W.x)*2,s=H/2),i=i.subtract(m(a,s,!0)).add(K).add(W),Rt(c,"leaflet-tooltip-right"),Rt(c,"leaflet-tooltip-left"),Rt(c,"leaflet-tooltip-top"),Rt(c,"leaflet-tooltip-bottom"),rt(c,"leaflet-tooltip-"+T),Gt(c,i)},_updatePosition:function(){var i=this._map.latLngToLayerPoint(this._latlng);this._setPosition(i)},setOpacity:function(i){this.options.opacity=i,this._container&&we(this._container,i)},_animateZoom:function(i){var a=this._map._latLngToNewLayerPoint(this._latlng,i.zoom,i.center);this._setPosition(a)},_getAnchor:function(){return m(this._source&&this._source._getTooltipAnchor&&!this.options.sticky?this._source._getTooltipAnchor():[0,0])}}),ku=function(i,a){return new cl(i,a)};_t.include({openTooltip:function(i,a,s){return this._initOverlay(cl,i,a,s).openOn(this),this},closeTooltip:function(i){return i.close(),this}}),Qe.include({bindTooltip:function(i,a){return this._tooltip&&this.isTooltipOpen()&&this.unbindTooltip(),this._tooltip=this._initOverlay(cl,this._tooltip,i,a),this._initTooltipInteractions(),this._tooltip.options.permanent&&this._map&&this._map.hasLayer(this)&&this.openTooltip(),this},unbindTooltip:function(){return this._tooltip&&(this._initTooltipInteractions(!0),this.closeTooltip(),this._tooltip=null),this},_initTooltipInteractions:function(i){if(!(!i&&this._tooltipHandlersAdded)){var a=i?"off":"on",s={remove:this.closeTooltip,move:this._moveTooltip};this._tooltip.options.permanent?s.add=this._openTooltip:(s.mouseover=this._openTooltip,s.mouseout=this.closeTooltip,s.click=this._openTooltip,this._map?this._addFocusListeners():s.add=this._addFocusListeners),this._tooltip.options.sticky&&(s.mousemove=this._moveTooltip),this[a](s),this._tooltipHandlersAdded=!i}},openTooltip:function(i){return this._tooltip&&(this instanceof Ne||(this._tooltip._source=this),this._tooltip._prepareOpen(i)&&(this._tooltip.openOn(this._map),this.getElement?this._setAriaDescribedByOnLayer(this):this.eachLayer&&this.eachLayer(this._setAriaDescribedByOnLayer,this))),this},closeTooltip:function(){if(this._tooltip)return this._tooltip.close()},toggleTooltip:function(){return this._tooltip&&this._tooltip.toggle(this),this},isTooltipOpen:function(){return this._tooltip.isOpen()},setTooltipContent:function(i){return this._tooltip&&this._tooltip.setContent(i),this},getTooltip:function(){return this._tooltip},_addFocusListeners:function(){this.getElement?this._addFocusListenersOnLayer(this):this.eachLayer&&this.eachLayer(this._addFocusListenersOnLayer,this)},_addFocusListenersOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&(ot(a,"focus",function(){this._tooltip._source=i,this.openTooltip()},this),ot(a,"blur",this.closeTooltip,this))},_setAriaDescribedByOnLayer:function(i){var a=typeof i.getElement=="function"&&i.getElement();a&&a.setAttribute("aria-describedby",this._tooltip._container.id)},_openTooltip:function(i){if(!(!this._tooltip||!this._map)){if(this._map.dragging&&this._map.dragging.moving()&&!this._openOnceFlag){this._openOnceFlag=!0;var a=this;this._map.once("moveend",function(){a._openOnceFlag=!1,a._openTooltip(i)});return}this._tooltip._source=i.layer||i.target,this.openTooltip(this._tooltip.options.sticky?i.latlng:void 0)}},_moveTooltip:function(i){var a=i.latlng,s,r;this._tooltip.options.sticky&&i.originalEvent&&(s=this._map.mouseEventToContainerPoint(i.originalEvent),r=this._map.containerPointToLayerPoint(s),a=this._map.layerPointToLatLng(r)),this._tooltip.setLatLng(a)}});var ns=Zn.extend({options:{iconSize:[12,12],html:!1,bgPos:null,className:"leaflet-div-icon"},createIcon:function(i){var a=i&&i.tagName==="DIV"?i:document.createElement("div"),s=this.options;if(s.html instanceof Element?(me(a),a.appendChild(s.html)):a.innerHTML=s.html!==!1?s.html:"",s.bgPos){var r=m(s.bgPos);a.style.backgroundPosition=-r.x+"px "+-r.y+"px"}return this._setIconStyles(a,"icon"),a},createShadow:function(){return null}});function xo(i){return new ns(i)}Zn.Default=jn;var ln=Qe.extend({options:{tileSize:256,opacity:1,updateWhenIdle:et.mobile,updateWhenZooming:!0,updateInterval:200,zIndex:1,bounds:null,minZoom:0,maxZoom:void 0,maxNativeZoom:void 0,minNativeZoom:void 0,noWrap:!1,pane:"tilePane",className:"",keepBuffer:2},initialize:function(i){mt(this,i)},onAdd:function(){this._initContainer(),this._levels={},this._tiles={},this._resetView()},beforeAdd:function(i){i._addZoomLimit(this)},onRemove:function(i){this._removeAllTiles(),Dt(this._container),i._removeZoomLimit(this),this._container=null,this._tileZoom=void 0},bringToFront:function(){return this._map&&(Ji(this._container),this._setAutoZIndex(Math.max)),this},bringToBack:function(){return this._map&&(En(this._container),this._setAutoZIndex(Math.min)),this},getContainer:function(){return this._container},setOpacity:function(i){return this.options.opacity=i,this._updateOpacity(),this},setZIndex:function(i){return this.options.zIndex=i,this._updateZIndex(),this},isLoading:function(){return this._loading},redraw:function(){if(this._map){this._removeAllTiles();var i=this._clampZoom(this._map.getZoom());i!==this._tileZoom&&(this._tileZoom=i,this._updateLevels()),this._update()}return this},getEvents:function(){var i={viewprereset:this._invalidateAll,viewreset:this._resetView,zoom:this._resetView,moveend:this._onMoveEnd};return this.options.updateWhenIdle||(this._onMove||(this._onMove=C(this._onMoveEnd,this.options.updateInterval,this)),i.move=this._onMove),this._zoomAnimated&&(i.zoomanim=this._animateZoom),i},createTile:function(){return document.createElement("div")},getTileSize:function(){var i=this.options.tileSize;return i instanceof P?i:new P(i,i)},_updateZIndex:function(){this._container&&this.options.zIndex!==void 0&&this.options.zIndex!==null&&(this._container.style.zIndex=this.options.zIndex)},_setAutoZIndex:function(i){for(var a=this.getPane().children,s=-i(-1/0,1/0),r=0,c=a.length,h;r<c;r++)h=a[r].style.zIndex,a[r]!==this._container&&h&&(s=i(s,+h));isFinite(s)&&(this.options.zIndex=s+i(-1,1),this._updateZIndex())},_updateOpacity:function(){if(this._map&&!et.ielt9){we(this._container,this.options.opacity);var i=+new Date,a=!1,s=!1;for(var r in this._tiles){var c=this._tiles[r];if(!(!c.current||!c.loaded)){var h=Math.min(1,(i-c.loaded)/200);we(c.el,h),h<1?a=!0:(c.active?s=!0:this._onOpaqueTile(c),c.active=!0)}}s&&!this._noPrune&&this._pruneTiles(),a&&(Et(this._fadeFrame),this._fadeFrame=Ut(this._updateOpacity,this))}},_onOpaqueTile:I,_initContainer:function(){this._container||(this._container=yt("div","leaflet-layer "+(this.options.className||"")),this._updateZIndex(),this.options.opacity<1&&this._updateOpacity(),this.getPane().appendChild(this._container))},_updateLevels:function(){var i=this._tileZoom,a=this.options.maxZoom;if(i!==void 0){for(var s in this._levels)s=Number(s),this._levels[s].el.children.length||s===i?(this._levels[s].el.style.zIndex=a-Math.abs(i-s),this._onUpdateLevel(s)):(Dt(this._levels[s].el),this._removeTilesAtZoom(s),this._onRemoveLevel(s),delete this._levels[s]);var r=this._levels[i],c=this._map;return r||(r=this._levels[i]={},r.el=yt("div","leaflet-tile-container leaflet-zoom-animated",this._container),r.el.style.zIndex=a,r.origin=c.project(c.unproject(c.getPixelOrigin()),i).round(),r.zoom=i,this._setZoomTransform(r,c.getCenter(),c.getZoom()),I(r.el.offsetWidth),this._onCreateLevel(r)),this._level=r,r}},_onUpdateLevel:I,_onRemoveLevel:I,_onCreateLevel:I,_pruneTiles:function(){if(this._map){var i,a,s=this._map.getZoom();if(s>this.options.maxZoom||s<this.options.minZoom){this._removeAllTiles();return}for(i in this._tiles)a=this._tiles[i],a.retain=a.current;for(i in this._tiles)if(a=this._tiles[i],a.current&&!a.active){var r=a.coords;this._retainParent(r.x,r.y,r.z,r.z-5)||this._retainChildren(r.x,r.y,r.z,r.z+2)}for(i in this._tiles)this._tiles[i].retain||this._removeTile(i)}},_removeTilesAtZoom:function(i){for(var a in this._tiles)this._tiles[a].coords.z===i&&this._removeTile(a)},_removeAllTiles:function(){for(var i in this._tiles)this._removeTile(i)},_invalidateAll:function(){for(var i in this._levels)Dt(this._levels[i].el),this._onRemoveLevel(Number(i)),delete this._levels[i];this._removeAllTiles(),this._tileZoom=void 0},_retainParent:function(i,a,s,r){var c=Math.floor(i/2),h=Math.floor(a/2),_=s-1,T=new P(+c,+h);T.z=+_;var N=this._tileCoordsToKey(T),H=this._tiles[N];return H&&H.active?(H.retain=!0,!0):(H&&H.loaded&&(H.retain=!0),_>r?this._retainParent(c,h,_,r):!1)},_retainChildren:function(i,a,s,r){for(var c=2*i;c<2*i+2;c++)for(var h=2*a;h<2*a+2;h++){var _=new P(c,h);_.z=s+1;var T=this._tileCoordsToKey(_),N=this._tiles[T];if(N&&N.active){N.retain=!0;continue}else N&&N.loaded&&(N.retain=!0);s+1<r&&this._retainChildren(c,h,s+1,r)}},_resetView:function(i){var a=i&&(i.pinch||i.flyTo);this._setView(this._map.getCenter(),this._map.getZoom(),a,a)},_animateZoom:function(i){this._setView(i.center,i.zoom,!0,i.noUpdate)},_clampZoom:function(i){var a=this.options;return a.minNativeZoom!==void 0&&i<a.minNativeZoom?a.minNativeZoom:a.maxNativeZoom!==void 0&&a.maxNativeZoom<i?a.maxNativeZoom:i},_setView:function(i,a,s,r){var c=Math.round(a);this.options.maxZoom!==void 0&&c>this.options.maxZoom||this.options.minZoom!==void 0&&c<this.options.minZoom?c=void 0:c=this._clampZoom(c);var h=this.options.updateWhenZooming&&c!==this._tileZoom;(!r||h)&&(this._tileZoom=c,this._abortLoading&&this._abortLoading(),this._updateLevels(),this._resetGrid(),c!==void 0&&this._update(i),s||this._pruneTiles(),this._noPrune=!!s),this._setZoomTransforms(i,a)},_setZoomTransforms:function(i,a){for(var s in this._levels)this._setZoomTransform(this._levels[s],i,a)},_setZoomTransform:function(i,a,s){var r=this._map.getZoomScale(s,i.zoom),c=i.origin.multiplyBy(r).subtract(this._map._getNewPixelOrigin(a,s)).round();et.any3d?qe(i.el,c,r):Gt(i.el,c)},_resetGrid:function(){var i=this._map,a=i.options.crs,s=this._tileSize=this.getTileSize(),r=this._tileZoom,c=this._map.getPixelWorldBounds(this._tileZoom);c&&(this._globalTileRange=this._pxBoundsToTileRange(c)),this._wrapX=a.wrapLng&&!this.options.noWrap&&[Math.floor(i.project([0,a.wrapLng[0]],r).x/s.x),Math.ceil(i.project([0,a.wrapLng[1]],r).x/s.y)],this._wrapY=a.wrapLat&&!this.options.noWrap&&[Math.floor(i.project([a.wrapLat[0],0],r).y/s.x),Math.ceil(i.project([a.wrapLat[1],0],r).y/s.y)]},_onMoveEnd:function(){!this._map||this._map._animatingZoom||this._update()},_getTiledPixelBounds:function(i){var a=this._map,s=a._animatingZoom?Math.max(a._animateToZoom,a.getZoom()):a.getZoom(),r=a.getZoomScale(s,this._tileZoom),c=a.project(i,this._tileZoom).floor(),h=a.getSize().divideBy(r*2);return new R(c.subtract(h),c.add(h))},_update:function(i){var a=this._map;if(a){var s=this._clampZoom(a.getZoom());if(i===void 0&&(i=a.getCenter()),this._tileZoom!==void 0){var r=this._getTiledPixelBounds(i),c=this._pxBoundsToTileRange(r),h=c.getCenter(),_=[],T=this.options.keepBuffer,N=new R(c.getBottomLeft().subtract([T,-T]),c.getTopRight().add([T,-T]));if(!(isFinite(c.min.x)&&isFinite(c.min.y)&&isFinite(c.max.x)&&isFinite(c.max.y)))throw new Error("Attempted to load an infinite number of tiles");for(var H in this._tiles){var K=this._tiles[H].coords;(K.z!==this._tileZoom||!N.contains(new P(K.x,K.y)))&&(this._tiles[H].current=!1)}if(Math.abs(s-this._tileZoom)>1){this._setView(i,s);return}for(var W=c.min.y;W<=c.max.y;W++)for(var tt=c.min.x;tt<=c.max.x;tt++){var st=new P(tt,W);if(st.z=this._tileZoom,!!this._isValidTile(st)){var Bt=this._tiles[this._tileCoordsToKey(st)];Bt?Bt.current=!0:_.push(st)}}if(_.sort(function(oe,ke){return oe.distanceTo(h)-ke.distanceTo(h)}),_.length!==0){this._loading||(this._loading=!0,this.fire("loading"));var Yt=document.createDocumentFragment();for(tt=0;tt<_.length;tt++)this._addTile(_[tt],Yt);this._level.el.appendChild(Yt)}}}},_isValidTile:function(i){var a=this._map.options.crs;if(!a.infinite){var s=this._globalTileRange;if(!a.wrapLng&&(i.x<s.min.x||i.x>s.max.x)||!a.wrapLat&&(i.y<s.min.y||i.y>s.max.y))return!1}if(!this.options.bounds)return!0;var r=this._tileCoordsToBounds(i);return J(this.options.bounds).overlaps(r)},_keyToBounds:function(i){return this._tileCoordsToBounds(this._keyToTileCoords(i))},_tileCoordsToNwSe:function(i){var a=this._map,s=this.getTileSize(),r=i.scaleBy(s),c=r.add(s),h=a.unproject(r,i.z),_=a.unproject(c,i.z);return[h,_]},_tileCoordsToBounds:function(i){var a=this._tileCoordsToNwSe(i),s=new G(a[0],a[1]);return this.options.noWrap||(s=this._map.wrapLatLngBounds(s)),s},_tileCoordsToKey:function(i){return i.x+":"+i.y+":"+i.z},_keyToTileCoords:function(i){var a=i.split(":"),s=new P(+a[0],+a[1]);return s.z=+a[2],s},_removeTile:function(i){var a=this._tiles[i];a&&(Dt(a.el),delete this._tiles[i],this.fire("tileunload",{tile:a.el,coords:this._keyToTileCoords(i)}))},_initTile:function(i){rt(i,"leaflet-tile");var a=this.getTileSize();i.style.width=a.x+"px",i.style.height=a.y+"px",i.onselectstart=I,i.onmousemove=I,et.ielt9&&this.options.opacity<1&&we(i,this.options.opacity)},_addTile:function(i,a){var s=this._getTilePos(i),r=this._tileCoordsToKey(i),c=this.createTile(this._wrapCoords(i),k(this._tileReady,this,i));this._initTile(c),this.createTile.length<2&&Ut(k(this._tileReady,this,i,null,c)),Gt(c,s),this._tiles[r]={el:c,coords:i,current:!0},a.appendChild(c),this.fire("tileloadstart",{tile:c,coords:i})},_tileReady:function(i,a,s){a&&this.fire("tileerror",{error:a,tile:s,coords:i});var r=this._tileCoordsToKey(i);s=this._tiles[r],s&&(s.loaded=+new Date,this._map._fadeAnimated?(we(s.el,0),Et(this._fadeFrame),this._fadeFrame=Ut(this._updateOpacity,this)):(s.active=!0,this._pruneTiles()),a||(rt(s.el,"leaflet-tile-loaded"),this.fire("tileload",{tile:s.el,coords:i})),this._noTilesToLoad()&&(this._loading=!1,this.fire("load"),et.ielt9||!this._map._fadeAnimated?Ut(this._pruneTiles,this):setTimeout(k(this._pruneTiles,this),250)))},_getTilePos:function(i){return i.scaleBy(this.getTileSize()).subtract(this._level.origin)},_wrapCoords:function(i){var a=new P(this._wrapX?q(i.x,this._wrapX):i.x,this._wrapY?q(i.y,this._wrapY):i.y);return a.z=i.z,a},_pxBoundsToTileRange:function(i){var a=this.getTileSize();return new R(i.min.unscaleBy(a).floor(),i.max.unscaleBy(a).ceil().subtract([1,1]))},_noTilesToLoad:function(){for(var i in this._tiles)if(!this._tiles[i].loaded)return!1;return!0}});function We(i){return new ln(i)}var sn=ln.extend({options:{minZoom:0,maxZoom:18,subdomains:"abc",errorTileUrl:"",zoomOffset:0,tms:!1,zoomReverse:!1,detectRetina:!1,crossOrigin:!1,referrerPolicy:!1},initialize:function(i,a){this._url=i,a=mt(this,a),a.detectRetina&&et.retina&&a.maxZoom>0?(a.tileSize=Math.floor(a.tileSize/2),a.zoomReverse?(a.zoomOffset--,a.minZoom=Math.min(a.maxZoom,a.minZoom+1)):(a.zoomOffset++,a.maxZoom=Math.max(a.minZoom,a.maxZoom-1)),a.minZoom=Math.max(0,a.minZoom)):a.zoomReverse?a.minZoom=Math.min(a.maxZoom,a.minZoom):a.maxZoom=Math.max(a.minZoom,a.maxZoom),typeof a.subdomains=="string"&&(a.subdomains=a.subdomains.split("")),this.on("tileunload",this._onTileRemove)},setUrl:function(i,a){return this._url===i&&a===void 0&&(a=!0),this._url=i,a||this.redraw(),this},createTile:function(i,a){var s=document.createElement("img");return ot(s,"load",k(this._tileOnLoad,this,a,s)),ot(s,"error",k(this._tileOnError,this,a,s)),(this.options.crossOrigin||this.options.crossOrigin==="")&&(s.crossOrigin=this.options.crossOrigin===!0?"":this.options.crossOrigin),typeof this.options.referrerPolicy=="string"&&(s.referrerPolicy=this.options.referrerPolicy),s.alt="",s.src=this.getTileUrl(i),s},getTileUrl:function(i){var a={r:et.retina?"@2x":"",s:this._getSubdomain(i),x:i.x,y:i.y,z:this._getZoomForUrl()};if(this._map&&!this._map.options.crs.infinite){var s=this._globalTileRange.max.y-i.y;this.options.tms&&(a.y=s),a["-y"]=s}return Ae(this._url,B(a,this.options))},_tileOnLoad:function(i,a){et.ielt9?setTimeout(k(i,this,null,a),0):i(null,a)},_tileOnError:function(i,a,s){var r=this.options.errorTileUrl;r&&a.getAttribute("src")!==r&&(a.src=r),i(s,a)},_onTileRemove:function(i){i.tile.onload=null},_getZoomForUrl:function(){var i=this._tileZoom,a=this.options.maxZoom,s=this.options.zoomReverse,r=this.options.zoomOffset;return s&&(i=a-i),i+r},_getSubdomain:function(i){var a=Math.abs(i.x+i.y)%this.options.subdomains.length;return this.options.subdomains[a]},_abortLoading:function(){var i,a;for(i in this._tiles)if(this._tiles[i].coords.z!==this._tileZoom&&(a=this._tiles[i].el,a.onload=I,a.onerror=I,!a.complete)){a.src=ht;var s=this._tiles[i].coords;Dt(a),delete this._tiles[i],this.fire("tileabort",{tile:a,coords:s})}},_removeTile:function(i){var a=this._tiles[i];if(a)return a.el.setAttribute("src",ht),ln.prototype._removeTile.call(this,i)},_tileReady:function(i,a,s){if(!(!this._map||s&&s.getAttribute("src")===ht))return ln.prototype._tileReady.call(this,i,a,s)}});function Re(i,a){return new sn(i,a)}var Be=sn.extend({defaultWmsParams:{service:"WMS",request:"GetMap",layers:"",styles:"",format:"image/jpeg",transparent:!1,version:"1.1.1"},options:{crs:null,uppercase:!1},initialize:function(i,a){this._url=i;var s=B({},this.defaultWmsParams);for(var r in a)r in this.options||(s[r]=a[r]);a=mt(this,a);var c=a.detectRetina&&et.retina?2:1,h=this.getTileSize();s.width=h.x*c,s.height=h.y*c,this.wmsParams=s},onAdd:function(i){this._crs=this.options.crs||i.options.crs,this._wmsVersion=parseFloat(this.wmsParams.version);var a=this._wmsVersion>=1.3?"crs":"srs";this.wmsParams[a]=this._crs.code,sn.prototype.onAdd.call(this,i)},getTileUrl:function(i){var a=this._tileCoordsToNwSe(i),s=this._crs,r=Y(s.project(a[0]),s.project(a[1])),c=r.min,h=r.max,_=(this._wmsVersion>=1.3&&this._crs===_o?[c.y,c.x,h.y,h.x]:[c.x,c.y,h.x,h.y]).join(","),T=sn.prototype.getTileUrl.call(this,i);return T+Xt(this.wmsParams,T,this.options.uppercase)+(this.options.uppercase?"&BBOX=":"&bbox=")+_},setParams:function(i,a){return B(this.wmsParams,i),a||this.redraw(),this}});function Pn(i,a){return new Be(i,a)}sn.WMS=Be,Re.wms=Pn;var Fe=Qe.extend({options:{padding:.1},initialize:function(i){mt(this,i),A(this),this._layers=this._layers||{}},onAdd:function(){this._container||(this._initContainer(),rt(this._container,"leaflet-zoom-animated")),this.getPane().appendChild(this._container),this._update(),this.on("update",this._updatePaths,this)},onRemove:function(){this.off("update",this._updatePaths,this),this._destroyContainer()},getEvents:function(){var i={viewreset:this._reset,zoom:this._onZoom,moveend:this._update,zoomend:this._onZoomEnd};return this._zoomAnimated&&(i.zoomanim=this._onAnimZoom),i},_onAnimZoom:function(i){this._updateTransform(i.center,i.zoom)},_onZoom:function(){this._updateTransform(this._map.getCenter(),this._map.getZoom())},_updateTransform:function(i,a){var s=this._map.getZoomScale(a,this._zoom),r=this._map.getSize().multiplyBy(.5+this.options.padding),c=this._map.project(this._center,a),h=r.multiplyBy(-s).add(c).subtract(this._map._getNewPixelOrigin(i,a));et.any3d?qe(this._container,h,s):Gt(this._container,h)},_reset:function(){this._update(),this._updateTransform(this._center,this._zoom);for(var i in this._layers)this._layers[i]._reset()},_onZoomEnd:function(){for(var i in this._layers)this._layers[i]._project()},_updatePaths:function(){for(var i in this._layers)this._layers[i]._update()},_update:function(){var i=this.options.padding,a=this._map.getSize(),s=this._map.containerPointToLayerPoint(a.multiplyBy(-i)).round();this._bounds=new R(s,s.add(a.multiplyBy(1+i*2)).round()),this._center=this._map.getCenter(),this._zoom=this._map.getZoom()}}),Na=Fe.extend({options:{tolerance:0},getEvents:function(){var i=Fe.prototype.getEvents.call(this);return i.viewprereset=this._onViewPreReset,i},_onViewPreReset:function(){this._postponeUpdatePaths=!0},onAdd:function(){Fe.prototype.onAdd.call(this),this._draw()},_initContainer:function(){var i=this._container=document.createElement("canvas");ot(i,"mousemove",this._onMouseMove,this),ot(i,"click dblclick mousedown mouseup contextmenu",this._onClick,this),ot(i,"mouseout",this._handleMouseOut,this),i._leaflet_disable_events=!0,this._ctx=i.getContext("2d")},_destroyContainer:function(){Et(this._redrawRequest),delete this._ctx,Dt(this._container),St(this._container),delete this._container},_updatePaths:function(){if(!this._postponeUpdatePaths){var i;this._redrawBounds=null;for(var a in this._layers)i=this._layers[a],i._update();this._redraw()}},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=this._container,s=i.getSize(),r=et.retina?2:1;Gt(a,i.min),a.width=r*s.x,a.height=r*s.y,a.style.width=s.x+"px",a.style.height=s.y+"px",et.retina&&this._ctx.scale(2,2),this._ctx.translate(-i.min.x,-i.min.y),this.fire("update")}},_reset:function(){Fe.prototype._reset.call(this),this._postponeUpdatePaths&&(this._postponeUpdatePaths=!1,this._updatePaths())},_initPath:function(i){this._updateDashArray(i),this._layers[A(i)]=i;var a=i._order={layer:i,prev:this._drawLast,next:null};this._drawLast&&(this._drawLast.next=a),this._drawLast=a,this._drawFirst=this._drawFirst||this._drawLast},_addPath:function(i){this._requestRedraw(i)},_removePath:function(i){var a=i._order,s=a.next,r=a.prev;s?s.prev=r:this._drawLast=r,r?r.next=s:this._drawFirst=s,delete i._order,delete this._layers[A(i)],this._requestRedraw(i)},_updatePath:function(i){this._extendRedrawBounds(i),i._project(),i._update(),this._requestRedraw(i)},_updateStyle:function(i){this._updateDashArray(i),this._requestRedraw(i)},_updateDashArray:function(i){if(typeof i.options.dashArray=="string"){var a=i.options.dashArray.split(/[, ]+/),s=[],r,c;for(c=0;c<a.length;c++){if(r=Number(a[c]),isNaN(r))return;s.push(r)}i.options._dashArray=s}else i.options._dashArray=i.options.dashArray},_requestRedraw:function(i){this._map&&(this._extendRedrawBounds(i),this._redrawRequest=this._redrawRequest||Ut(this._redraw,this))},_extendRedrawBounds:function(i){if(i._pxBounds){var a=(i.options.weight||0)+1;this._redrawBounds=this._redrawBounds||new R,this._redrawBounds.extend(i._pxBounds.min.subtract([a,a])),this._redrawBounds.extend(i._pxBounds.max.add([a,a]))}},_redraw:function(){this._redrawRequest=null,this._redrawBounds&&(this._redrawBounds.min._floor(),this._redrawBounds.max._ceil()),this._clear(),this._draw(),this._redrawBounds=null},_clear:function(){var i=this._redrawBounds;if(i){var a=i.getSize();this._ctx.clearRect(i.min.x,i.min.y,a.x,a.y)}else this._ctx.save(),this._ctx.setTransform(1,0,0,1,0,0),this._ctx.clearRect(0,0,this._container.width,this._container.height),this._ctx.restore()},_draw:function(){var i,a=this._redrawBounds;if(this._ctx.save(),a){var s=a.getSize();this._ctx.beginPath(),this._ctx.rect(a.min.x,a.min.y,s.x,s.y),this._ctx.clip()}this._drawing=!0;for(var r=this._drawFirst;r;r=r.next)i=r.layer,(!a||i._pxBounds&&i._pxBounds.intersects(a))&&i._updatePath();this._drawing=!1,this._ctx.restore()},_updatePoly:function(i,a){if(this._drawing){var s,r,c,h,_=i._parts,T=_.length,N=this._ctx;if(T){for(N.beginPath(),s=0;s<T;s++){for(r=0,c=_[s].length;r<c;r++)h=_[s][r],N[r?"lineTo":"moveTo"](h.x,h.y);a&&N.closePath()}this._fillStroke(N,i)}}},_updateCircle:function(i){if(!(!this._drawing||i._empty())){var a=i._point,s=this._ctx,r=Math.max(Math.round(i._radius),1),c=(Math.max(Math.round(i._radiusY),1)||r)/r;c!==1&&(s.save(),s.scale(1,c)),s.beginPath(),s.arc(a.x,a.y/c,r,0,Math.PI*2,!1),c!==1&&s.restore(),this._fillStroke(s,i)}},_fillStroke:function(i,a){var s=a.options;s.fill&&(i.globalAlpha=s.fillOpacity,i.fillStyle=s.fillColor||s.color,i.fill(s.fillRule||"evenodd")),s.stroke&&s.weight!==0&&(i.setLineDash&&i.setLineDash(a.options&&a.options._dashArray||[]),i.globalAlpha=s.opacity,i.lineWidth=s.weight,i.strokeStyle=s.color,i.lineCap=s.lineCap,i.lineJoin=s.lineJoin,i.stroke())},_onClick:function(i){for(var a=this._map.mouseEventToLayerPoint(i),s,r,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(!(i.type==="click"||i.type==="preclick")||!this._map._draggableMoved(s))&&(r=s);this._fireEvent(r?[r]:!1,i)},_onMouseMove:function(i){if(!(!this._map||this._map.dragging.moving()||this._map._animatingZoom)){var a=this._map.mouseEventToLayerPoint(i);this._handleMouseHover(i,a)}},_handleMouseOut:function(i){var a=this._hoveredLayer;a&&(Rt(this._container,"leaflet-interactive"),this._fireEvent([a],i,"mouseout"),this._hoveredLayer=null,this._mouseHoverThrottled=!1)},_handleMouseHover:function(i,a){if(!this._mouseHoverThrottled){for(var s,r,c=this._drawFirst;c;c=c.next)s=c.layer,s.options.interactive&&s._containsPoint(a)&&(r=s);r!==this._hoveredLayer&&(this._handleMouseOut(i),r&&(rt(this._container,"leaflet-interactive"),this._fireEvent([r],i,"mouseover"),this._hoveredLayer=r)),this._fireEvent(this._hoveredLayer?[this._hoveredLayer]:!1,i),this._mouseHoverThrottled=!0,setTimeout(k(function(){this._mouseHoverThrottled=!1},this),32)}},_fireEvent:function(i,a,s){this._map._fireDOMEvent(a,s||a.type,i)},_bringToFront:function(i){var a=i._order;if(a){var s=a.next,r=a.prev;if(s)s.prev=r;else return;r?r.next=s:s&&(this._drawFirst=s),a.prev=this._drawLast,this._drawLast.next=a,a.next=null,this._drawLast=a,this._requestRedraw(i)}},_bringToBack:function(i){var a=i._order;if(a){var s=a.next,r=a.prev;if(r)r.next=s;else return;s?s.prev=r:r&&(this._drawLast=r),a.prev=null,a.next=this._drawFirst,this._drawFirst.prev=a,this._drawFirst=a,this._requestRedraw(i)}}});function Ca(i){return et.canvas?new Na(i):null}var on=function(){try{return document.namespaces.add("lvml","urn:schemas-microsoft-com:vml"),function(i){return document.createElement("<lvml:"+i+' class="lvml">')}}catch{}return function(i){return document.createElement("<"+i+' xmlns="urn:schemas-microsoft.com:vml" class="lvml">')}}(),Gn={_initContainer:function(){this._container=yt("div","leaflet-vml-container")},_update:function(){this._map._animatingZoom||(Fe.prototype._update.call(this),this.fire("update"))},_initPath:function(i){var a=i._container=on("shape");rt(a,"leaflet-vml-shape "+(this.options.className||"")),a.coordsize="1 1",i._path=on("path"),a.appendChild(i._path),this._updateStyle(i),this._layers[A(i)]=i},_addPath:function(i){var a=i._container;this._container.appendChild(a),i.options.interactive&&i.addInteractiveTarget(a)},_removePath:function(i){var a=i._container;Dt(a),i.removeInteractiveTarget(a),delete this._layers[A(i)]},_updateStyle:function(i){var a=i._stroke,s=i._fill,r=i.options,c=i._container;c.stroked=!!r.stroke,c.filled=!!r.fill,r.stroke?(a||(a=i._stroke=on("stroke")),c.appendChild(a),a.weight=r.weight+"px",a.color=r.color,a.opacity=r.opacity,r.dashArray?a.dashStyle=wt(r.dashArray)?r.dashArray.join(" "):r.dashArray.replace(/( *, *)/g," "):a.dashStyle="",a.endcap=r.lineCap.replace("butt","flat"),a.joinstyle=r.lineJoin):a&&(c.removeChild(a),i._stroke=null),r.fill?(s||(s=i._fill=on("fill")),c.appendChild(s),s.color=r.fillColor||r.color,s.opacity=r.fillOpacity):s&&(c.removeChild(s),i._fill=null)},_updateCircle:function(i){var a=i._point.round(),s=Math.round(i._radius),r=Math.round(i._radiusY||s);this._setPath(i,i._empty()?"M0 0":"AL "+a.x+","+a.y+" "+s+","+r+" 0,"+65535*360)},_setPath:function(i,a){i._path.v=a},_bringToFront:function(i){Ji(i._container)},_bringToBack:function(i){En(i._container)}},Da=et.vml?on:Ps,Ci=Fe.extend({_initContainer:function(){this._container=Da("svg"),this._container.setAttribute("pointer-events","none"),this._rootGroup=Da("g"),this._container.appendChild(this._rootGroup)},_destroyContainer:function(){Dt(this._container),St(this._container),delete this._container,delete this._rootGroup,delete this._svgSize},_update:function(){if(!(this._map._animatingZoom&&this._bounds)){Fe.prototype._update.call(this);var i=this._bounds,a=i.getSize(),s=this._container;(!this._svgSize||!this._svgSize.equals(a))&&(this._svgSize=a,s.setAttribute("width",a.x),s.setAttribute("height",a.y)),Gt(s,i.min),s.setAttribute("viewBox",[i.min.x,i.min.y,a.x,a.y].join(" ")),this.fire("update")}},_initPath:function(i){var a=i._path=Da("path");i.options.className&&rt(a,i.options.className),i.options.interactive&&rt(a,"leaflet-interactive"),this._updateStyle(i),this._layers[A(i)]=i},_addPath:function(i){this._rootGroup||this._initContainer(),this._rootGroup.appendChild(i._path),i.addInteractiveTarget(i._path)},_removePath:function(i){Dt(i._path),i.removeInteractiveTarget(i._path),delete this._layers[A(i)]},_updatePath:function(i){i._project(),i._update()},_updateStyle:function(i){var a=i._path,s=i.options;a&&(s.stroke?(a.setAttribute("stroke",s.color),a.setAttribute("stroke-opacity",s.opacity),a.setAttribute("stroke-width",s.weight),a.setAttribute("stroke-linecap",s.lineCap),a.setAttribute("stroke-linejoin",s.lineJoin),s.dashArray?a.setAttribute("stroke-dasharray",s.dashArray):a.removeAttribute("stroke-dasharray"),s.dashOffset?a.setAttribute("stroke-dashoffset",s.dashOffset):a.removeAttribute("stroke-dashoffset")):a.setAttribute("stroke","none"),s.fill?(a.setAttribute("fill",s.fillColor||s.color),a.setAttribute("fill-opacity",s.fillOpacity),a.setAttribute("fill-rule",s.fillRule||"evenodd")):a.setAttribute("fill","none"))},_updatePoly:function(i,a){this._setPath(i,Gs(i._parts,a))},_updateCircle:function(i){var a=i._point,s=Math.max(Math.round(i._radius),1),r=Math.max(Math.round(i._radiusY),1)||s,c="a"+s+","+r+" 0 1,0 ",h=i._empty()?"M0 0":"M"+(a.x-s)+","+a.y+c+s*2+",0 "+c+-s*2+",0 ";this._setPath(i,h)},_setPath:function(i,a){i._path.setAttribute("d",a)},_bringToFront:function(i){Ji(i._path)},_bringToBack:function(i){En(i._path)}});et.vml&&Ci.include(Gn);function un(i){return et.svg||et.vml?new Ci(i):null}_t.include({getRenderer:function(i){var a=i.options.renderer||this._getPaneRenderer(i.options.pane)||this.options.renderer||this._renderer;return a||(a=this._renderer=this._createRenderer()),this.hasLayer(a)||this.addLayer(a),a},_getPaneRenderer:function(i){if(i==="overlayPane"||i===void 0)return!1;var a=this._paneRenderers[i];return a===void 0&&(a=this._createRenderer({pane:i}),this._paneRenderers[i]=a),a},_createRenderer:function(i){return this.options.preferCanvas&&Ca(i)||un(i)}});var So=Hn.extend({initialize:function(i,a){Hn.prototype.initialize.call(this,this._boundsToLatLngs(i),a)},setBounds:function(i){return this.setLatLngs(this._boundsToLatLngs(i))},_boundsToLatLngs:function(i){return i=J(i),[i.getSouthWest(),i.getNorthWest(),i.getNorthEast(),i.getSouthEast()]}});function Ze(i,a){return new So(i,a)}Ci.create=Da,Ci.pointsToPath=Gs,De.geometryToLayer=za,De.coordsToLatLng=sl,De.coordsToLatLngs=La,De.latLngToCoords=Aa,De.latLngsToCoords=ol,De.getFeature=Ke,De.asFeature=qn,_t.mergeOptions({boxZoom:!0});var fl=Xe.extend({initialize:function(i){this._map=i,this._container=i._container,this._pane=i._panes.overlayPane,this._resetStateTimeout=0,i.on("unload",this._destroy,this)},addHooks:function(){ot(this._container,"mousedown",this._onMouseDown,this)},removeHooks:function(){St(this._container,"mousedown",this._onMouseDown,this)},moved:function(){return this._moved},_destroy:function(){Dt(this._pane),delete this._pane},_resetState:function(){this._resetStateTimeout=0,this._moved=!1},_clearDeferredResetState:function(){this._resetStateTimeout!==0&&(clearTimeout(this._resetStateTimeout),this._resetStateTimeout=0)},_onMouseDown:function(i){if(!i.shiftKey||i.which!==1&&i.button!==1)return!1;this._clearDeferredResetState(),this._resetState(),ai(),ba(),this._startPoint=this._map.mouseEventToContainerPoint(i),ot(document,{contextmenu:oi,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseMove:function(i){this._moved||(this._moved=!0,this._box=yt("div","leaflet-zoom-box",this._container),rt(this._container,"leaflet-crosshair"),this._map.fire("boxzoomstart")),this._point=this._map.mouseEventToContainerPoint(i);var a=new R(this._point,this._startPoint),s=a.getSize();Gt(this._box,a.min),this._box.style.width=s.x+"px",this._box.style.height=s.y+"px"},_finish:function(){this._moved&&(Dt(this._box),Rt(this._container,"leaflet-crosshair")),ya(),Yl(),St(document,{contextmenu:oi,mousemove:this._onMouseMove,mouseup:this._onMouseUp,keydown:this._onKeyDown},this)},_onMouseUp:function(i){if(!(i.which!==1&&i.button!==1)&&(this._finish(),!!this._moved)){this._clearDeferredResetState(),this._resetStateTimeout=setTimeout(k(this._resetState,this),0);var a=new G(this._map.containerPointToLatLng(this._startPoint),this._map.containerPointToLatLng(this._point));this._map.fitBounds(a).fire("boxzoomend",{boxZoomBounds:a})}},_onKeyDown:function(i){i.keyCode===27&&(this._finish(),this._clearDeferredResetState(),this._resetState())}});_t.addInitHook("addHandler","boxZoom",fl),_t.mergeOptions({doubleClickZoom:!0});var ri=Xe.extend({addHooks:function(){this._map.on("dblclick",this._onDoubleClick,this)},removeHooks:function(){this._map.off("dblclick",this._onDoubleClick,this)},_onDoubleClick:function(i){var a=this._map,s=a.getZoom(),r=a.options.zoomDelta,c=i.originalEvent.shiftKey?s-r:s+r;a.options.doubleClickZoom==="center"?a.setZoom(c):a.setZoomAround(i.containerPoint,c)}});_t.addInitHook("addHandler","doubleClickZoom",ri),_t.mergeOptions({dragging:!0,inertia:!0,inertiaDeceleration:3400,inertiaMaxSpeed:1/0,easeLinearity:.2,worldCopyJump:!1,maxBoundsViscosity:0});var as=Xe.extend({addHooks:function(){if(!this._draggable){var i=this._map;this._draggable=new Oi(i._mapPane,i._container),this._draggable.on({dragstart:this._onDragStart,drag:this._onDrag,dragend:this._onDragEnd},this),this._draggable.on("predrag",this._onPreDragLimit,this),i.options.worldCopyJump&&(this._draggable.on("predrag",this._onPreDragWrap,this),i.on("zoomend",this._onZoomEnd,this),i.whenReady(this._onZoomEnd,this))}rt(this._map._container,"leaflet-grab leaflet-touch-drag"),this._draggable.enable(),this._positions=[],this._times=[]},removeHooks:function(){Rt(this._map._container,"leaflet-grab"),Rt(this._map._container,"leaflet-touch-drag"),this._draggable.disable()},moved:function(){return this._draggable&&this._draggable._moved},moving:function(){return this._draggable&&this._draggable._moving},_onDragStart:function(){var i=this._map;if(i._stop(),this._map.options.maxBounds&&this._map.options.maxBoundsViscosity){var a=J(this._map.options.maxBounds);this._offsetLimit=Y(this._map.latLngToContainerPoint(a.getNorthWest()).multiplyBy(-1),this._map.latLngToContainerPoint(a.getSouthEast()).multiplyBy(-1).add(this._map.getSize())),this._viscosity=Math.min(1,Math.max(0,this._map.options.maxBoundsViscosity))}else this._offsetLimit=null;i.fire("movestart").fire("dragstart"),i.options.inertia&&(this._positions=[],this._times=[])},_onDrag:function(i){if(this._map.options.inertia){var a=this._lastTime=+new Date,s=this._lastPos=this._draggable._absPos||this._draggable._newPos;this._positions.push(s),this._times.push(a),this._prunePositions(a)}this._map.fire("move",i).fire("drag",i)},_prunePositions:function(i){for(;this._positions.length>1&&i-this._times[0]>50;)this._positions.shift(),this._times.shift()},_onZoomEnd:function(){var i=this._map.getSize().divideBy(2),a=this._map.latLngToLayerPoint([0,0]);this._initialWorldOffset=a.subtract(i).x,this._worldWidth=this._map.getPixelWorldBounds().getSize().x},_viscousLimit:function(i,a){return i-(i-a)*this._viscosity},_onPreDragLimit:function(){if(!(!this._viscosity||!this._offsetLimit)){var i=this._draggable._newPos.subtract(this._draggable._startPos),a=this._offsetLimit;i.x<a.min.x&&(i.x=this._viscousLimit(i.x,a.min.x)),i.y<a.min.y&&(i.y=this._viscousLimit(i.y,a.min.y)),i.x>a.max.x&&(i.x=this._viscousLimit(i.x,a.max.x)),i.y>a.max.y&&(i.y=this._viscousLimit(i.y,a.max.y)),this._draggable._newPos=this._draggable._startPos.add(i)}},_onPreDragWrap:function(){var i=this._worldWidth,a=Math.round(i/2),s=this._initialWorldOffset,r=this._draggable._newPos.x,c=(r-a+s)%i+a-s,h=(r+a+s)%i-a-s,_=Math.abs(c+s)<Math.abs(h+s)?c:h;this._draggable._absPos=this._draggable._newPos.clone(),this._draggable._newPos.x=_},_onDragEnd:function(i){var a=this._map,s=a.options,r=!s.inertia||i.noInertia||this._times.length<2;if(a.fire("dragend",i),r)a.fire("moveend");else{this._prunePositions(+new Date);var c=this._lastPos.subtract(this._positions[0]),h=(this._lastTime-this._times[0])/1e3,_=s.easeLinearity,T=c.multiplyBy(_/h),N=T.distanceTo([0,0]),H=Math.min(s.inertiaMaxSpeed,N),K=T.multiplyBy(H/N),W=H/(s.inertiaDeceleration*_),tt=K.multiplyBy(-W/2).round();!tt.x&&!tt.y?a.fire("moveend"):(tt=a._limitOffset(tt,a.options.maxBounds),Ut(function(){a.panBy(tt,{duration:W,easeLinearity:_,noMoveStart:!0,animate:!0})}))}}});_t.addInitHook("addHandler","dragging",as),_t.mergeOptions({keyboard:!0,keyboardPanDelta:80});var Ra=Xe.extend({keyCodes:{left:[37],right:[39],down:[40],up:[38],zoomIn:[187,107,61,171],zoomOut:[189,109,54,173]},initialize:function(i){this._map=i,this._setPanDelta(i.options.keyboardPanDelta),this._setZoomDelta(i.options.zoomDelta)},addHooks:function(){var i=this._map._container;i.tabIndex<=0&&(i.tabIndex="0"),ot(i,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.on({focus:this._addHooks,blur:this._removeHooks},this)},removeHooks:function(){this._removeHooks(),St(this._map._container,{focus:this._onFocus,blur:this._onBlur,mousedown:this._onMouseDown},this),this._map.off({focus:this._addHooks,blur:this._removeHooks},this)},_onMouseDown:function(){if(!this._focused){var i=document.body,a=document.documentElement,s=i.scrollTop||a.scrollTop,r=i.scrollLeft||a.scrollLeft;this._map._container.focus(),window.scrollTo(r,s)}},_onFocus:function(){this._focused=!0,this._map.fire("focus")},_onBlur:function(){this._focused=!1,this._map.fire("blur")},_setPanDelta:function(i){var a=this._panKeys={},s=this.keyCodes,r,c;for(r=0,c=s.left.length;r<c;r++)a[s.left[r]]=[-1*i,0];for(r=0,c=s.right.length;r<c;r++)a[s.right[r]]=[i,0];for(r=0,c=s.down.length;r<c;r++)a[s.down[r]]=[0,i];for(r=0,c=s.up.length;r<c;r++)a[s.up[r]]=[0,-1*i]},_setZoomDelta:function(i){var a=this._zoomKeys={},s=this.keyCodes,r,c;for(r=0,c=s.zoomIn.length;r<c;r++)a[s.zoomIn[r]]=i;for(r=0,c=s.zoomOut.length;r<c;r++)a[s.zoomOut[r]]=-i},_addHooks:function(){ot(document,"keydown",this._onKeyDown,this)},_removeHooks:function(){St(document,"keydown",this._onKeyDown,this)},_onKeyDown:function(i){if(!(i.altKey||i.ctrlKey||i.metaKey)){var a=i.keyCode,s=this._map,r;if(a in this._panKeys){if(!s._panAnim||!s._panAnim._inProgress)if(r=this._panKeys[a],i.shiftKey&&(r=m(r).multiplyBy(3)),s.options.maxBounds&&(r=s._limitOffset(m(r),s.options.maxBounds)),s.options.worldCopyJump){var c=s.wrapLatLng(s.unproject(s.project(s.getCenter()).add(r)));s.panTo(c)}else s.panBy(r)}else if(a in this._zoomKeys)s.setZoom(s.getZoom()+(i.shiftKey?3:1)*this._zoomKeys[a]);else if(a===27&&s._popup&&s._popup.options.closeOnEscapeKey)s.closePopup();else return;oi(i)}}});_t.addInitHook("addHandler","keyboard",Ra),_t.mergeOptions({scrollWheelZoom:!0,wheelDebounceTime:40,wheelPxPerZoomLevel:60});var Di=Xe.extend({addHooks:function(){ot(this._map._container,"wheel",this._onWheelScroll,this),this._delta=0},removeHooks:function(){St(this._map._container,"wheel",this._onWheelScroll,this)},_onWheelScroll:function(i){var a=Ta(i),s=this._map.options.wheelDebounceTime;this._delta+=a,this._lastMousePos=this._map.mouseEventToContainerPoint(i),this._startTime||(this._startTime=+new Date);var r=Math.max(s-(+new Date-this._startTime),0);clearTimeout(this._timer),this._timer=setTimeout(k(this._performZoom,this),r),oi(i)},_performZoom:function(){var i=this._map,a=i.getZoom(),s=this._map.options.zoomSnap||0;i._stop();var r=this._delta/(this._map.options.wheelPxPerZoomLevel*4),c=4*Math.log(2/(1+Math.exp(-Math.abs(r))))/Math.LN2,h=s?Math.ceil(c/s)*s:c,_=i._limitZoom(a+(this._delta>0?h:-h))-a;this._delta=0,this._startTime=null,_&&(i.options.scrollWheelZoom==="center"?i.setZoom(a+_):i.setZoomAround(this._lastMousePos,a+_))}});_t.addInitHook("addHandler","scrollWheelZoom",Di);var ls=600;_t.mergeOptions({tapHold:et.touchNative&&et.safari&&et.mobile,tapTolerance:15});var hl=Xe.extend({addHooks:function(){ot(this._map._container,"touchstart",this._onDown,this)},removeHooks:function(){St(this._map._container,"touchstart",this._onDown,this)},_onDown:function(i){if(clearTimeout(this._holdTimeout),i.touches.length===1){var a=i.touches[0];this._startPos=this._newPos=new P(a.clientX,a.clientY),this._holdTimeout=setTimeout(k(function(){this._cancel(),this._isTapValid()&&(ot(document,"touchend",Qt),ot(document,"touchend touchcancel",this._cancelClickPrevent),this._simulateEvent("contextmenu",a))},this),ls),ot(document,"touchend touchcancel contextmenu",this._cancel,this),ot(document,"touchmove",this._onMove,this)}},_cancelClickPrevent:function i(){St(document,"touchend",Qt),St(document,"touchend touchcancel",i)},_cancel:function(){clearTimeout(this._holdTimeout),St(document,"touchend touchcancel contextmenu",this._cancel,this),St(document,"touchmove",this._onMove,this)},_onMove:function(i){var a=i.touches[0];this._newPos=new P(a.clientX,a.clientY)},_isTapValid:function(){return this._newPos.distanceTo(this._startPos)<=this._map.options.tapTolerance},_simulateEvent:function(i,a){var s=new MouseEvent(i,{bubbles:!0,cancelable:!0,view:window,screenX:a.screenX,screenY:a.screenY,clientX:a.clientX,clientY:a.clientY});s._simulated=!0,a.target.dispatchEvent(s)}});_t.addInitHook("addHandler","tapHold",hl),_t.mergeOptions({touchZoom:et.touch,bounceAtZoomLimits:!0});var rn=Xe.extend({addHooks:function(){rt(this._map._container,"leaflet-touch-zoom"),ot(this._map._container,"touchstart",this._onTouchStart,this)},removeHooks:function(){Rt(this._map._container,"leaflet-touch-zoom"),St(this._map._container,"touchstart",this._onTouchStart,this)},_onTouchStart:function(i){var a=this._map;if(!(!i.touches||i.touches.length!==2||a._animatingZoom||this._zooming)){var s=a.mouseEventToContainerPoint(i.touches[0]),r=a.mouseEventToContainerPoint(i.touches[1]);this._centerPoint=a.getSize()._divideBy(2),this._startLatLng=a.containerPointToLatLng(this._centerPoint),a.options.touchZoom!=="center"&&(this._pinchStartLatLng=a.containerPointToLatLng(s.add(r)._divideBy(2))),this._startDist=s.distanceTo(r),this._startZoom=a.getZoom(),this._moved=!1,this._zooming=!0,a._stop(),ot(document,"touchmove",this._onTouchMove,this),ot(document,"touchend touchcancel",this._onTouchEnd,this),Qt(i)}},_onTouchMove:function(i){if(!(!i.touches||i.touches.length!==2||!this._zooming)){var a=this._map,s=a.mouseEventToContainerPoint(i.touches[0]),r=a.mouseEventToContainerPoint(i.touches[1]),c=s.distanceTo(r)/this._startDist;if(this._zoom=a.getScaleZoom(c,this._startZoom),!a.options.bounceAtZoomLimits&&(this._zoom<a.getMinZoom()&&c<1||this._zoom>a.getMaxZoom()&&c>1)&&(this._zoom=a._limitZoom(this._zoom)),a.options.touchZoom==="center"){if(this._center=this._startLatLng,c===1)return}else{var h=s._add(r)._divideBy(2)._subtract(this._centerPoint);if(c===1&&h.x===0&&h.y===0)return;this._center=a.unproject(a.project(this._pinchStartLatLng,this._zoom).subtract(h),this._zoom)}this._moved||(a._moveStart(!0,!1),this._moved=!0),Et(this._animRequest);var _=k(a._move,a,this._center,this._zoom,{pinch:!0,round:!1},void 0);this._animRequest=Ut(_,this,!0),Qt(i)}},_onTouchEnd:function(){if(!this._moved||!this._zooming){this._zooming=!1;return}this._zooming=!1,Et(this._animRequest),St(document,"touchmove",this._onTouchMove,this),St(document,"touchend touchcancel",this._onTouchEnd,this),this._map.options.zoomAnimation?this._map._animateZoom(this._center,this._map._limitZoom(this._zoom),!0,this._map.options.zoomSnap):this._map._resetView(this._center,this._map._limitZoom(this._zoom))}});_t.addInitHook("addHandler","touchZoom",rn),_t.BoxZoom=fl,_t.DoubleClickZoom=ri,_t.Drag=as,_t.Keyboard=Ra,_t.ScrollWheelZoom=Di,_t.TapHold=hl,_t.TouchZoom=rn,b.Bounds=R,b.Browser=et,b.CRS=Pt,b.Canvas=Na,b.Circle=es,b.CircleMarker=Ea,b.Class=de,b.Control=Me,b.DivIcon=ns,b.DivOverlay=Je,b.DomEvent=Eu,b.DomUtil=no,b.Draggable=Oi,b.Evented=Q,b.FeatureGroup=Ne,b.GeoJSON=De,b.GridLayer=ln,b.Handler=Xe,b.Icon=Zn,b.ImageOverlay=ui,b.LatLng=$,b.LatLngBounds=G,b.Layer=Qe,b.LayerGroup=nn,b.LineUtil=ho,b.Map=_t,b.Marker=Un,b.Mixin=Nu,b.Path=yi,b.Point=P,b.PolyUtil=Cu,b.Polygon=Hn,b.Polyline=bi,b.Popup=Oa,b.PosAnimation=tl,b.Projection=mo,b.Rectangle=So,b.Renderer=Fe,b.SVG=Ci,b.SVGOverlay=Ni,b.TileLayer=sn,b.Tooltip=cl,b.Transformation=yn,b.Util=qi,b.VideoOverlay=rl,b.bind=k,b.bounds=Y,b.canvas=Ca,b.circle=Uu,b.circleMarker=vo,b.control=Rn,b.divIcon=xo,b.extend=B,b.featureGroup=Ma,b.geoJSON=ul,b.geoJson=go,b.gridLayer=We,b.icon=ll,b.imageOverlay=kn,b.latLng=X,b.latLngBounds=J,b.layerGroup=po,b.map=wa,b.marker=ts,b.point=m,b.polygon=Ce,b.polyline=Hu,b.popup=qu,b.rectangle=Ze,b.setOptions=mt,b.stamp=A,b.svg=un,b.svgOverlay=bo,b.tileLayer=Re,b.tooltip=ku,b.transformation=ki,b.version=v,b.videoOverlay=yo;var Yn=window.L;b.noConflict=function(){return window.L=Yn,this},window.L=b})}(ks,ks.exports)),ks.exports}var G_=P_();const gn=Ed(G_),Y_=({map:g})=>{const[E,b]=ie.useState(!0),{selectedMapType:v,setSelectedMapType:B,species:V,toggleSpeciesVisibility:k}=da(),ct=[{id:"terrain",name:"地形图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"streets",name:"街道图",url:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"},{id:"satellite",name:"卫星图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"},{id:"hybrid",name:"混合图",url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"}],A=[{range:"600+",color:"bg-red-700",count:"600+"},{range:"500-600",color:"bg-red-600",count:"500 - 600"},{range:"400-500",color:"bg-red-500",count:"400 - 500"},{range:"300-400",color:"bg-orange-500",count:"300 - 400"},{range:"250-300",color:"bg-orange-400",count:"250 - 300"},{range:"200-250",color:"bg-yellow-500",count:"200 - 250"},{range:"150-200",color:"bg-yellow-400",count:"150 - 200"},{range:"100-150",color:"bg-yellow-300",count:"100 - 150"},{range:"50-100",color:"bg-green-300",count:"50 - 100"},{range:"15-50",color:"bg-green-200",count:"15 - 50"},{range:"0-15",color:"bg-green-100",count:"0 - 15"}],C=q=>{g&&(B(q.id),g.eachLayer(I=>{I instanceof gn.TileLayer&&g.removeLayer(I)}),gn.tileLayer(q.url,{attribution:q.id==="satellite"||q.id==="hybrid"?"© Esri":"© OpenStreetMap contributors",maxZoom:18}).addTo(g))};return x.jsx("div",{className:"absolute top-4 right-4 z-[1000]",children:x.jsxs("div",{className:"map-controls",children:[x.jsx("button",{onClick:()=>b(!E),className:"flex items-center justify-center w-8 h-8 bg-white rounded-md shadow-md hover:bg-gray-50 mb-2",children:x.jsx(O_,{className:"h-4 w-4 text-gray-600"})}),E&&x.jsxs("div",{className:"space-y-4",children:[x.jsxs("div",{children:[x.jsx("h6",{className:"text-sm font-semibold text-gray-900 mb-2",children:"地图类型"}),x.jsx("div",{className:"space-y-1",children:ct.map(q=>x.jsxs("label",{className:"flex items-center cursor-pointer",children:[x.jsx("input",{type:"radio",name:"mapType",value:q.id,checked:v===q.id,onChange:()=>C(q),className:"mr-2 text-blue-600"}),x.jsx("span",{className:"text-sm text-gray-700",children:q.name})]},q.id))})]}),x.jsxs("div",{children:[x.jsx("div",{className:"flex items-center mb-2",children:x.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"物种图层"})}),x.jsx("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:V.map(q=>x.jsxs("div",{className:"flex items-center justify-between",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"w-3 h-3 rounded-full mr-2 border border-gray-300",style:{backgroundColor:q.color}}),x.jsx("span",{className:"text-xs text-gray-700",children:q.name})]}),x.jsx("button",{onClick:()=>k(q.id),className:"p-1 hover:bg-gray-100 rounded",children:q.isVisible?x.jsx(Ld,{className:"h-3 w-3 text-gray-600"}):x.jsx(d_,{className:"h-3 w-3 text-gray-400"})})]},q.id))})]}),x.jsxs("div",{children:[x.jsxs("div",{className:"flex items-center mb-2",children:[x.jsx("h5",{className:"text-sm font-semibold text-gray-900",children:"观察到的鸟种"}),x.jsx("button",{className:"ml-2",children:x.jsx(y_,{className:"h-3 w-3 text-gray-500"})})]}),x.jsx("div",{className:"space-y-1",children:A.map(q=>x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:`w-4 h-3 ${q.color} mr-2 border border-gray-300`}),x.jsx("span",{className:"text-xs text-gray-700",children:q.count})]},q.range))})]})]})]})})};(function(){function g(E){return this instanceof g?(this._canvas=E=typeof E=="string"?document.getElementById(E):E,this._ctx=E.getContext("2d"),this._width=E.width,this._height=E.height,this._max=1,void this.clear()):new g(E)}g.prototype={defaultRadius:25,defaultGradient:{.4:"blue",.6:"cyan",.7:"lime",.8:"yellow",1:"red"},data:function(E,b){return this._data=E,this},max:function(E){return this._max=E,this},add:function(E){return this._data.push(E),this},clear:function(){return this._data=[],this},radius:function(E,b){b=b||15;var v=this._circle=document.createElement("canvas"),B=v.getContext("2d"),V=this._r=E+b;return v.width=v.height=2*V,B.shadowOffsetX=B.shadowOffsetY=200,B.shadowBlur=b,B.shadowColor="black",B.beginPath(),B.arc(V-200,V-200,E,0,2*Math.PI,!0),B.closePath(),B.fill(),this},gradient:function(E){var b=document.createElement("canvas"),v=b.getContext("2d"),B=v.createLinearGradient(0,0,0,256);b.width=1,b.height=256;for(var V in E)B.addColorStop(V,E[V]);return v.fillStyle=B,v.fillRect(0,0,1,256),this._grad=v.getImageData(0,0,1,256).data,this},draw:function(E){this._circle||this.radius(this.defaultRadius),this._grad||this.gradient(this.defaultGradient);var b=this._ctx;b.clearRect(0,0,this._width,this._height);for(var v,B=0,V=this._data.length;V>B;B++)v=this._data[B],b.globalAlpha=Math.max(v[2]/this._max,E||.05),b.drawImage(this._circle,v[0]-this._r,v[1]-this._r);var k=b.getImageData(0,0,this._width,this._height);return this._colorize(k.data,this._grad),b.putImageData(k,0,0),this},_colorize:function(E,b){for(var v,B=3,V=E.length;V>B;B+=4)v=4*E[B],v&&(E[B-3]=b[v],E[B-2]=b[v+1],E[B-1]=b[v+2])}},window.simpleheat=g})(),L.HeatLayer=(L.Layer?L.Layer:L.Class).extend({initialize:function(g,E){this._latlngs=g,L.setOptions(this,E)},setLatLngs:function(g){return this._latlngs=g,this.redraw()},addLatLng:function(g){return this._latlngs.push(g),this.redraw()},setOptions:function(g){return L.setOptions(this,g),this._heat&&this._updateOptions(),this.redraw()},redraw:function(){return!this._heat||this._frame||this._map._animating||(this._frame=L.Util.requestAnimFrame(this._redraw,this)),this},onAdd:function(g){this._map=g,this._canvas||this._initCanvas(),g._panes.overlayPane.appendChild(this._canvas),g.on("moveend",this._reset,this),g.options.zoomAnimation&&L.Browser.any3d&&g.on("zoomanim",this._animateZoom,this),this._reset()},onRemove:function(g){g.getPanes().overlayPane.removeChild(this._canvas),g.off("moveend",this._reset,this),g.options.zoomAnimation&&g.off("zoomanim",this._animateZoom,this)},addTo:function(g){return g.addLayer(this),this},_initCanvas:function(){var g=this._canvas=L.DomUtil.create("canvas","leaflet-heatmap-layer leaflet-layer"),E=L.DomUtil.testProp(["transformOrigin","WebkitTransformOrigin","msTransformOrigin"]);g.style[E]="50% 50%";var b=this._map.getSize();g.width=b.x,g.height=b.y;var v=this._map.options.zoomAnimation&&L.Browser.any3d;L.DomUtil.addClass(g,"leaflet-zoom-"+(v?"animated":"hide")),this._heat=simpleheat(g),this._updateOptions()},_updateOptions:function(){this._heat.radius(this.options.radius||this._heat.defaultRadius,this.options.blur),this.options.gradient&&this._heat.gradient(this.options.gradient),this.options.max&&this._heat.max(this.options.max)},_reset:function(){var g=this._map.containerPointToLayerPoint([0,0]);L.DomUtil.setPosition(this._canvas,g);var E=this._map.getSize();this._heat._width!==E.x&&(this._canvas.width=this._heat._width=E.x),this._heat._height!==E.y&&(this._canvas.height=this._heat._height=E.y),this._redraw()},_redraw:function(){var g,E,b,v,B,V,k,ct,A,C=[],q=this._heat._r,I=this._map.getSize(),ut=new L.Bounds(L.point([-q,-q]),I.add([q,q])),kt=this.options.max===void 0?1:this.options.max,jt=this.options.maxZoom===void 0?this._map.getMaxZoom():this.options.maxZoom,mt=1/Math.pow(2,Math.max(0,Math.min(jt-this._map.getZoom(),12))),Xt=q/2,ne=[],Ae=this._map._getMapPanePos(),wt=Ae.x%Xt,fe=Ae.y%Xt;for(g=0,E=this._latlngs.length;E>g;g++)if(b=this._map.latLngToContainerPoint(this._latlngs[g]),ut.contains(b)){B=Math.floor((b.x-wt)/Xt)+2,V=Math.floor((b.y-fe)/Xt)+2;var ht=this._latlngs[g].alt!==void 0?this._latlngs[g].alt:this._latlngs[g][2]!==void 0?+this._latlngs[g][2]:1;A=ht*mt,ne[V]=ne[V]||[],v=ne[V][B],v?(v[0]=(v[0]*v[2]+b.x*A)/(v[2]+A),v[1]=(v[1]*v[2]+b.y*A)/(v[2]+A),v[2]+=A):ne[V][B]=[b.x,b.y,A]}for(g=0,E=ne.length;E>g;g++)if(ne[g])for(k=0,ct=ne[g].length;ct>k;k++)v=ne[g][k],v&&C.push([Math.round(v[0]),Math.round(v[1]),Math.min(v[2],kt)]);this._heat.data(C).draw(this.options.minOpacity),this._frame=null},_animateZoom:function(g){var E=this._map.getZoomScale(g.zoom),b=this._map._getCenterOffset(g.center)._multiplyBy(-E).subtract(this._map._getMapPanePos());L.DomUtil.setTransform?L.DomUtil.setTransform(this._canvas,b,E):this._canvas.style[L.DomUtil.TRANSFORM]=L.DomUtil.getTranslateString(b)+" scale("+E+")"}}),L.heatLayer=function(g,E){return new L.HeatLayer(g,E)};const V_=({map:g})=>{const E=ie.useRef({}),{species:b,observations:v,searchQuery:B}=da();return ie.useEffect(()=>g?(Object.values(E.current).forEach(k=>{g.hasLayer(k)&&g.removeLayer(k)}),E.current={},b.filter(k=>{if(!k.isVisible)return!1;if(B.trim()){const ct=B.toLowerCase();return k.name.toLowerCase().includes(ct)||k.scientificName.toLowerCase().includes(ct)}return!0}).forEach(k=>{const ct=v.filter(C=>C.speciesId===k.id);if(ct.length===0)return;const A=gn.layerGroup();ct.forEach(C=>{const q=gn.circleMarker([C.lat,C.lng],{radius:Math.max(3,C.intensity*15),fillColor:k.color,color:k.color,weight:1,opacity:.8,fillOpacity:Math.max(.3,C.intensity*.7)});q.bindPopup(`
          <div class="text-sm">
            <strong>${k.name}</strong><br/>
            <em>${k.scientificName}</em><br/>
            观察数量: ${C.count}
          </div>
        `),A.addLayer(q)}),A.addTo(g),E.current[k.id]=A}),()=>{Object.values(E.current).forEach(k=>{g.hasLayer(k)&&g.removeLayer(k)})}):void 0,[g,b,v,B]),null},X_=()=>{const[g,E]=ie.useState(!1),[b,v]=ie.useState(""),{species:B,toggleSpeciesVisibility:V}=da(),k=B.filter(A=>A.name.toLowerCase().includes(b.toLowerCase())||A.scientificName.toLowerCase().includes(b.toLowerCase())),ct=B.filter(A=>A.isVisible).length;return x.jsx("div",{className:"absolute top-20 left-4 z-[1000] max-w-xs sm:max-w-sm animate-slide-up",children:x.jsxs("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 overflow-hidden",children:[x.jsxs("button",{onClick:()=>E(!g),className:"flex items-center justify-between w-full px-5 py-4 text-left hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100/50 transition-all duration-200 group",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3 group-hover:scale-105 transition-transform duration-200",children:x.jsx(Sd,{className:"h-4 w-4 text-white"})}),x.jsxs("span",{className:"text-sm font-semibold text-gray-900",children:["物种筛选 (",ct,"/",B.length,")"]})]}),x.jsx(Od,{className:`h-5 w-5 text-gray-400 transition-all duration-300 group-hover:text-primary-600 ${g?"rotate-45 text-primary-600":""}`})]}),g&&x.jsxs("div",{className:"border-t border-gray-200/50 p-5 w-full sm:w-80 animate-fade-in",children:[x.jsxs("div",{className:"relative mb-5 group",children:[x.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl blur opacity-20 group-hover:opacity-30 transition-opacity duration-200"}),x.jsxs("div",{className:"relative",children:[x.jsx(Ad,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 group-hover:text-primary-500 transition-colors duration-200"}),x.jsx("input",{type:"text",placeholder:"搜索物种...",value:b,onChange:A=>v(A.target.value),className:"w-full pl-11 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm bg-white shadow-soft hover:shadow-medium transition-all duration-200"})]})]}),x.jsx("div",{className:"space-y-2 max-h-60 overflow-y-auto custom-scrollbar",children:k.map(A=>x.jsxs("div",{className:"flex items-center justify-between p-3 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100/50 rounded-lg cursor-pointer transition-all duration-200 group border border-transparent hover:border-gray-200/50",onClick:()=>V(A.id),children:[x.jsxs("div",{className:"flex items-center flex-1",children:[x.jsx("div",{className:"w-5 h-5 rounded-full mr-3 border-2 border-white shadow-sm flex-shrink-0 transition-all duration-200 group-hover:scale-110",style:{backgroundColor:A.isVisible?A.color:"transparent",borderColor:A.isVisible?A.color:"#d1d5db"}}),x.jsxs("div",{className:"flex-1 min-w-0",children:[x.jsx("div",{className:"text-sm font-semibold text-gray-900 truncate group-hover:text-primary-700 transition-colors duration-200",children:A.name}),x.jsx("div",{className:"text-xs text-gray-500 truncate",children:A.scientificName})]})]}),x.jsx("input",{type:"checkbox",checked:A.isVisible,onChange:()=>V(A.id),className:"ml-3 w-4 h-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-all duration-200",onClick:C=>C.stopPropagation()})]},A.id))}),k.length===0&&x.jsxs("div",{className:"text-center py-6 text-gray-500 text-sm bg-gray-50/50 rounded-lg border border-gray-200/50",children:[x.jsx(Sd,{className:"h-8 w-8 text-gray-300 mx-auto mb-2"}),"未找到匹配的物种"]}),x.jsx("div",{className:"mt-5 pt-4 border-t border-gray-200/50",children:x.jsxs("div",{className:"flex space-x-3",children:[x.jsx("button",{onClick:()=>{B.forEach(A=>{A.isVisible||V(A.id)})},className:"flex-1 px-4 py-2 text-sm bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-soft hover:shadow-medium font-medium",children:"全选"}),x.jsx("button",{onClick:()=>{B.forEach(A=>{A.isVisible&&V(A.id)})},className:"flex-1 px-4 py-2 text-sm bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-lg hover:from-gray-200 hover:to-gray-300 transition-all duration-200 shadow-soft hover:shadow-medium font-medium",children:"全不选"})]})})]})]})})},Q_=()=>{const{species:g,observations:E,searchQuery:b}=da(),v=g.filter(A=>{if(!A.isVisible)return!1;if(b.trim()){const C=b.toLowerCase();return A.name.toLowerCase().includes(C)||A.scientificName.toLowerCase().includes(C)}return!0}),B=E.filter(A=>v.some(C=>C.id===A.speciesId)),V=B.length,k=B.reduce((A,C)=>A+C.count,0),ct=V>0?Math.round(k/V):0;return x.jsx("div",{className:"absolute bottom-4 left-4 z-[1000] max-w-xs animate-slide-up",children:x.jsxs("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl shadow-large border border-white/20 p-4 sm:p-5 hover:shadow-xl transition-all duration-300",children:[x.jsxs("div",{className:"flex items-center mb-4",children:[x.jsx("div",{className:"flex items-center justify-center w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg mr-3",children:x.jsx(r_,{className:"h-4 w-4 text-white"})}),x.jsx("h3",{className:"text-base font-bold text-gray-900",children:"观察统计"})]}),x.jsxs("div",{className:"space-y-3 text-sm",children:[x.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100/50 rounded-lg border border-blue-200/50",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"flex items-center justify-center w-6 h-6 bg-blue-500 rounded-md mr-3",children:x.jsx(Ld,{className:"h-3 w-3 text-white"})}),x.jsx("span",{className:"text-gray-700 font-medium",children:"显示物种:"})]}),x.jsxs("span",{className:"font-bold text-blue-700 text-base",children:[v.length,"/",g.length]})]}),x.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100/50 rounded-lg border border-green-200/50",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"flex items-center justify-center w-6 h-6 bg-green-500 rounded-md mr-3",children:x.jsx(M_,{className:"h-3 w-3 text-white"})}),x.jsx("span",{className:"text-gray-700 font-medium",children:"观察点:"})]}),x.jsx("span",{className:"font-bold text-green-700 text-base",children:V.toLocaleString()})]}),x.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-purple-50 to-purple-100/50 rounded-lg border border-purple-200/50",children:[x.jsx("span",{className:"text-gray-700 font-medium",children:"总观察数:"}),x.jsx("span",{className:"font-bold text-purple-700 text-base",children:k.toLocaleString()})]}),x.jsxs("div",{className:"flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100/50 rounded-lg border border-orange-200/50",children:[x.jsx("span",{className:"text-gray-700 font-medium",children:"平均数量:"}),x.jsx("span",{className:"font-bold text-orange-700 text-base",children:ct})]})]}),v.length>0&&x.jsxs("div",{className:"mt-5 pt-4 border-t border-gray-200/50",children:[x.jsxs("div",{className:"text-sm font-semibold text-gray-700 mb-3 flex items-center",children:[x.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-primary-500 to-primary-600 rounded-full mr-2"}),"物种分布"]}),x.jsx("div",{className:"space-y-2",children:v.map(A=>{const q=E.filter(I=>I.speciesId===A.id).reduce((I,ut)=>I+ut.count,0);return x.jsxs("div",{className:"flex items-center justify-between p-2 bg-gray-50/80 rounded-lg hover:bg-gray-100/80 transition-colors duration-200",children:[x.jsxs("div",{className:"flex items-center",children:[x.jsx("div",{className:"w-3 h-3 rounded-full mr-3 shadow-sm border border-white",style:{backgroundColor:A.color}}),x.jsx("span",{className:"text-gray-700 font-medium text-sm truncate max-w-24",children:A.name})]}),x.jsx("span",{className:"text-gray-900 font-bold text-sm",children:q.toLocaleString()})]},A.id)})})]})]})})},K_=({message:g="加载中..."})=>x.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-[2000]",children:x.jsxs("div",{className:"text-center",children:[x.jsx(T_,{className:"h-8 w-8 animate-spin text-blue-600 mx-auto mb-2"}),x.jsx("p",{className:"text-sm text-gray-600",children:g})]})}),J_=()=>{const[g,E]=ie.useState(!1),b=[{key:"1-5",description:"切换对应物种的显示/隐藏"},{key:"Ctrl+A",description:"全选/全不选物种"},{key:"Esc",description:"关闭面板"}];return x.jsxs(x.Fragment,{children:[x.jsx("button",{onClick:()=>E(!0),className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-[1000] bg-white hover:bg-gray-50 rounded-full p-2 shadow-lg border border-gray-200",title:"帮助和快捷键",children:x.jsx(f_,{className:"h-5 w-5 text-gray-600"})}),g&&x.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[3000] p-4",children:x.jsxs("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto",children:[x.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[x.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"帮助和快捷键"}),x.jsx("button",{onClick:()=>E(!1),className:"p-1 hover:bg-gray-100 rounded",children:x.jsx(Od,{className:"h-5 w-5 text-gray-500"})})]}),x.jsxs("div",{className:"p-4 space-y-6",children:[x.jsxs("div",{children:[x.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"关于此应用"}),x.jsx("p",{className:"text-sm text-gray-600",children:"这是一个基于 eBird 的观鸟热点地图应用，展示了不同物种在各地区的分布情况。 您可以通过筛选和搜索功能来探索不同物种的观察数据。"})]}),x.jsxs("div",{children:[x.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"主要功能"}),x.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[x.jsx("li",{children:"• 交互式地图显示物种分布"}),x.jsx("li",{children:"• 多物种图层叠加"}),x.jsx("li",{children:"• 物种筛选和搜索"}),x.jsx("li",{children:"• 地图类型切换"}),x.jsx("li",{children:"• 实时统计信息"})]})]}),x.jsxs("div",{children:[x.jsxs("div",{className:"flex items-center mb-2",children:[x.jsx(x_,{className:"h-4 w-4 text-gray-600 mr-2"}),x.jsx("h3",{className:"text-md font-medium text-gray-900",children:"键盘快捷键"})]}),x.jsx("div",{className:"space-y-2",children:b.map((v,B)=>x.jsxs("div",{className:"flex items-center justify-between text-sm",children:[x.jsx("span",{className:"font-mono bg-gray-100 px-2 py-1 rounded text-gray-800",children:v.key}),x.jsx("span",{className:"text-gray-600 flex-1 ml-3",children:v.description})]},B))})]}),x.jsxs("div",{children:[x.jsx("h3",{className:"text-md font-medium text-gray-900 mb-2",children:"地图操作"}),x.jsxs("ul",{className:"text-sm text-gray-600 space-y-1",children:[x.jsx("li",{children:"• 拖拽移动地图"}),x.jsx("li",{children:"• 滚轮缩放"}),x.jsx("li",{children:"• 点击标记查看详情"}),x.jsx("li",{children:"• 使用右侧控制面板切换图层"})]})]})]}),x.jsx("div",{className:"p-4 border-t border-gray-200 bg-gray-50",children:x.jsx("p",{className:"text-xs text-gray-500 text-center",children:"基于 React + Leaflet 构建 | 数据为模拟数据"})})]})})]})};delete gn.Icon.Default.prototype._getIconUrl;gn.Icon.Default.mergeOptions({iconRetinaUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",iconUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",shadowUrl:"https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png"});const W_=()=>{const g=ie.useRef(null),E=ie.useRef(null),[b,v]=ie.useState(!0),{mapCenter:B,mapZoom:V,setMapCenter:k,setMapZoom:ct}=da();return ie.useEffect(()=>{if(!g.current||E.current)return;const A=gn.map(g.current,{center:B,zoom:V,zoomControl:!1}),C=gn.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",{attribution:"© OpenStreetMap contributors",maxZoom:18});return C.on("load",()=>{v(!1)}),C.addTo(A),gn.control.zoom({position:"bottomright"}).addTo(A),A.on("moveend",()=>{const q=A.getCenter();k([q.lat,q.lng])}),A.on("zoomend",()=>{ct(A.getZoom())}),E.current=A,()=>{E.current&&(E.current.remove(),E.current=null)}},[k,ct]),x.jsxs("div",{className:"relative w-full h-full",children:[x.jsx("div",{ref:g,className:"w-full h-full"}),b&&x.jsx(K_,{message:"正在加载地图..."}),x.jsx(V_,{map:E.current}),x.jsx(X_,{}),x.jsx(Q_,{}),x.jsx(J_,{}),x.jsx(Y_,{map:E.current})]})},F_=()=>{const{species:g,toggleSpeciesVisibility:E}=da();ie.useEffect(()=>{const b=v=>{if(!(v.target instanceof HTMLInputElement||v.target instanceof HTMLTextAreaElement))switch(v.key.toLowerCase()){case"1":case"2":case"3":case"4":case"5":v.preventDefault();const B=parseInt(v.key)-1;g[B]&&E(g[B].id);break;case"a":if(v.ctrlKey||v.metaKey){v.preventDefault();const V=g.every(k=>k.isVisible);g.forEach(k=>{(V&&k.isVisible||!V&&!k.isVisible)&&E(k.id)})}break;case"escape":v.preventDefault();break}};return document.addEventListener("keydown",b),()=>{document.removeEventListener("keydown",b)}},[g,E])};function I_(){const g=da(E=>E.initializeData);return F_(),ie.useEffect(()=>{g()},[g]),x.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-100 animate-fade-in",children:[x.jsx(R_,{}),x.jsxs("main",{className:"flex flex-col h-screen",children:[x.jsx(q_,{}),x.jsx("div",{className:"flex-1 relative",children:x.jsx(W_,{})})]})]})}t_.createRoot(document.getElementById("root")).render(x.jsx(ie.StrictMode,{children:x.jsx(I_,{})}));
