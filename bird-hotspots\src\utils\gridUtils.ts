import type { ObservationPoint, GridCell, GridConfig } from '../types';

/**
 * 根据缩放级别获取网格大小
 * 缩放级别越高，网格越小，显示越详细
 */
export function getGridSize(zoomLevel: number): number {
  if (zoomLevel <= 4) return 2.0;      // 2度网格 - 国家级别
  if (zoomLevel <= 6) return 1.0;      // 1度网格 - 省级别
  if (zoomLevel <= 8) return 0.5;      // 0.5度网格 - 市级别
  if (zoomLevel <= 10) return 0.2;     // 0.2度网格 - 区县级别
  if (zoomLevel <= 12) return 0.1;     // 0.1度网格 - 街道级别
  if (zoomLevel <= 14) return 0.05;    // 0.05度网格 - 社区级别
  return 0.02;                         // 0.02度网格 - 详细级别
}

/**
 * 获取缩放阈值，决定何时显示网格vs具体点
 */
export function getDisplayThreshold(zoomLevel: number): 'grid' | 'points' {
  return zoomLevel >= 15 ? 'points' : 'grid';
}

/**
 * 将经纬度坐标转换为网格坐标
 */
export function getGridCoordinates(lat: number, lng: number, gridSize: number): { gridLat: number; gridLng: number } {
  return {
    gridLat: Math.floor(lat / gridSize) * gridSize,
    gridLng: Math.floor(lng / gridSize) * gridSize
  };
}

/**
 * 根据网格坐标和大小计算网格边界
 */
export function getGridBounds(gridLat: number, gridLng: number, gridSize: number) {
  return {
    north: gridLat + gridSize,
    south: gridLat,
    east: gridLng + gridSize,
    west: gridLng
  };
}

/**
 * 创建网格单元的唯一键
 */
export function getGridKey(gridLat: number, gridLng: number): string {
  return `${gridLat.toFixed(6)},${gridLng.toFixed(6)}`;
}

/**
 * 将观察数据聚合到网格中
 */
export function aggregateObservationsToGrid(
  observations: ObservationPoint[],
  gridSize: number,
  speciesFilter?: string[]
): Map<string, GridCell> {
  const gridMap = new Map<string, GridCell>();

  // 过滤观察数据
  const filteredObservations = speciesFilter 
    ? observations.filter(obs => speciesFilter.includes(obs.speciesId))
    : observations;

  filteredObservations.forEach(obs => {
    const { gridLat, gridLng } = getGridCoordinates(obs.lat, obs.lng, gridSize);
    const gridKey = getGridKey(gridLat, gridLng);
    
    if (!gridMap.has(gridKey)) {
      // 创建新的网格单元
      gridMap.set(gridKey, {
        gridLat,
        gridLng,
        bounds: getGridBounds(gridLat, gridLng, gridSize),
        totalCount: 0,
        observationCount: 0,
        averageIntensity: 0,
        speciesIds: [],
        observations: []
      });
    }

    const gridCell = gridMap.get(gridKey)!;
    
    // 更新网格单元数据
    gridCell.totalCount += obs.count;
    gridCell.observationCount += 1;
    gridCell.observations.push(obs);
    
    // 更新物种列表
    if (!gridCell.speciesIds.includes(obs.speciesId)) {
      gridCell.speciesIds.push(obs.speciesId);
    }
  });

  // 计算平均强度
  gridMap.forEach(gridCell => {
    if (gridCell.observationCount > 0) {
      gridCell.averageIntensity = gridCell.observations.reduce((sum, obs) => sum + obs.intensity, 0) / gridCell.observationCount;
    }
  });

  return gridMap;
}

/**
 * 根据地图边界过滤网格单元
 */
export function filterGridByBounds(
  gridMap: Map<string, GridCell>,
  bounds: { north: number; south: number; east: number; west: number }
): Map<string, GridCell> {
  const filteredGrid = new Map<string, GridCell>();

  gridMap.forEach((gridCell, key) => {
    // 检查网格是否与地图边界相交
    if (gridCell.bounds.north >= bounds.south &&
        gridCell.bounds.south <= bounds.north &&
        gridCell.bounds.east >= bounds.west &&
        gridCell.bounds.west <= bounds.east) {
      filteredGrid.set(key, gridCell);
    }
  });

  return filteredGrid;
}

/**
 * 计算网格单元的显示优先级（用于性能优化）
 */
export function calculateGridPriority(gridCell: GridCell, mapCenter: [number, number]): number {
  const centerLat = mapCenter[0];
  const centerLng = mapCenter[1];
  const gridCenterLat = gridCell.gridLat + (gridCell.bounds.north - gridCell.bounds.south) / 2;
  const gridCenterLng = gridCell.gridLng + (gridCell.bounds.east - gridCell.bounds.west) / 2;
  
  // 计算距离地图中心的距离
  const distance = Math.sqrt(
    Math.pow(gridCenterLat - centerLat, 2) + 
    Math.pow(gridCenterLng - centerLng, 2)
  );
  
  // 优先级 = 观察数量 / 距离，距离越近、观察数量越多优先级越高
  return gridCell.totalCount / (distance + 0.1);
}

/**
 * 获取网格单元的统计信息文本
 */
export function getGridStatsText(gridCell: GridCell): string {
  const speciesCount = gridCell.speciesIds.length;
  const avgIntensity = (gridCell.averageIntensity * 100).toFixed(1);
  
  return `
    <div class="text-sm p-2">
      <div class="font-bold text-gray-800 mb-2">网格区域统计</div>
      <div class="space-y-1">
        <div class="flex justify-between">
          <span class="text-gray-700">观察总数:</span>
          <span class="font-bold text-blue-600">${gridCell.totalCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">观察点数:</span>
          <span class="font-medium text-green-600">${gridCell.observationCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">物种数量:</span>
          <span class="font-medium text-purple-600">${speciesCount}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-gray-700">平均强度:</span>
          <span class="font-medium text-orange-600">${avgIntensity}%</span>
        </div>
      </div>
    </div>
  `;
}

/**
 * 性能优化：限制显示的网格数量
 */
export function limitGridCells(
  gridMap: Map<string, GridCell>,
  maxCells: number = 1000,
  mapCenter: [number, number]
): Map<string, GridCell> {
  if (gridMap.size <= maxCells) {
    return gridMap;
  }

  // 按优先级排序并限制数量
  const sortedCells = Array.from(gridMap.entries())
    .map(([key, cell]) => ({
      key,
      cell,
      priority: calculateGridPriority(cell, mapCenter)
    }))
    .sort((a, b) => b.priority - a.priority)
    .slice(0, maxCells);

  const limitedGrid = new Map<string, GridCell>();
  sortedCells.forEach(({ key, cell }) => {
    limitedGrid.set(key, cell);
  });

  return limitedGrid;
}
